#!/usr/bin/env python3
"""
测试LightGBM回归模型
"""

import sys
import os
sys.path.append('./models/lightgbm')

from lgbm_regression import LightGBMStockRegressionModel, run_lgbm_regression

def test_regression_model():
    """测试回归模型"""
    print("=== 测试LightGBM回归模型 ===")
    
    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'  # 数据文件
    model_path = './test_model_lgbm_regression.pkl'  # 模型保存路径
    batch_size = 200  # 较小的批次用于测试
    validation_size = 50  # 较小的验证集用于测试
    
    # 自定义LightGBM参数（较快的训练参数）
    custom_params = {
        'num_leaves': 15,
        'learning_rate': 0.1,
        'n_estimators': 50,
        'random_state': 42
    }
    
    try:
        # 检查数据文件是否存在
        if not os.path.exists(csv_file):
            print(f"数据文件不存在: {csv_file}")
            print("请确保数据文件存在或修改csv_file路径")
            return
        
        # 运行模型
        print("开始运行回归模型...")
        results = run_lgbm_regression(
            csv_file=csv_file,
            model_path=model_path,
            batch_size=batch_size,
            validation_size=validation_size,
            custom_params=custom_params
        )
        
        print("\n=== 测试结果 ===")
        if 'overall_rmse' in results:
            # 滚动验证结果
            print(f"总体 RMSE: {results['overall_rmse']:.6f}")
            print(f"总体 MAE: {results['overall_mae']:.6f}")
            print(f"总体 R²: {results['overall_r2']:.4f}")
            print(f"总验证样本数: {len(results['predictions'])}")
            print(f"总训练轮次: {results['total_rounds']}")
        else:
            # 测试结果
            print(f"RMSE: {results['rmse']:.6f}")
            print(f"MAE: {results['mae']:.6f}")
            print(f"R²: {results['r2']:.4f}")
            print(f"测试样本数: {len(results['predictions'])}")
        
        print("\n测试成功完成！")
        
        # 清理测试文件
        if os.path.exists(model_path):
            os.remove(model_path)
            print(f"已清理测试模型文件: {model_path}")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_regression_model()
