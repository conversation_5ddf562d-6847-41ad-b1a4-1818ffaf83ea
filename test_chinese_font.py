#!/usr/bin/env python3
"""
测试中文字体显示
"""

import matplotlib.pyplot as plt
import matplotlib
import numpy as np

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Sim<PERSON>ei', 'DejaVu Sans', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

def test_chinese_plot():
    """测试中文图表显示"""
    print("测试中文图表显示...")
    
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 绘制数据
    ax.plot(x, y1, label='正弦波', linewidth=2)
    ax.plot(x, y2, label='余弦波', linewidth=2)
    
    # 设置中文标签
    ax.set_title('中文字体测试 - 三角函数图', fontsize=16)
    ax.set_xlabel('时间 (秒)', fontsize=14)
    ax.set_ylabel('幅度', fontsize=14)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加文本注释
    ax.text(2, 0.5, '这是中文注释', fontsize=12, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('./chinese_font_test_simple.png', dpi=150, bbox_inches='tight')
    print("中文测试图已保存到: ./chinese_font_test_simple.png")
    plt.show()

if __name__ == "__main__":
    test_chinese_plot()
