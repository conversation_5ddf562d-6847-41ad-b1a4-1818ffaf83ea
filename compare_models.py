#!/usr/bin/env python3
"""
对比原始模型和改进模型的效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def compare_prediction_results():
    """对比两个模型的预测结果"""
    
    print("=== 模型效果对比分析 ===")
    
    # 读取原始结果
    try:
        original_df = pd.read_csv('simple_prediction_results.csv')
        print(f"原始模型结果: {len(original_df)} 个样本")
    except FileNotFoundError:
        print("未找到原始模型结果文件")
        return
    
    # 读取改进结果
    try:
        improved_df = pd.read_csv('improved_prediction_results.csv')
        print(f"改进模型结果: {len(improved_df)} 个样本")
    except FileNotFoundError:
        print("未找到改进模型结果文件，请先运行 improved_lgbm_regression.py")
        return
    
    # 基本统计对比
    print("\n=== 基本统计对比 ===")
    
    def analyze_model(df, model_name):
        actual = df['actual_return']
        predicted = df['predicted_return']
        
        # 基本指标
        correlation = actual.corr(predicted)
        mse = ((predicted - actual) ** 2).mean()
        mae = abs(predicted - actual).mean()
        rmse = np.sqrt(mse)
        r2 = 1 - mse / actual.var()
        
        # 标准差比率
        std_ratio = predicted.std() / actual.std()
        
        print(f"\n{model_name}:")
        print(f"  相关系数: {correlation:.4f}")
        print(f"  RMSE: {rmse:.6f}")
        print(f"  MAE: {mae:.6f}")
        print(f"  R²: {r2:.4f}")
        print(f"  预测标准差: {predicted.std():.6f}")
        print(f"  真实标准差: {actual.std():.6f}")
        print(f"  标准差比率: {std_ratio:.4f}")
        
        return {
            'correlation': correlation,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'pred_std': predicted.std(),
            'true_std': actual.std(),
            'std_ratio': std_ratio
        }
    
    original_stats = analyze_model(original_df, "原始模型")
    improved_stats = analyze_model(improved_df, "改进模型")
    
    # 极值预测能力对比
    print("\n=== 极值预测能力对比 ===")
    
    def analyze_extreme_prediction(df, model_name):
        actual = df['actual_return']
        predicted = df['predicted_return']
        
        # 定义极值阈值
        q10 = actual.quantile(0.1)
        q90 = actual.quantile(0.9)
        
        # 大涨情况
        large_up_mask = actual >= q90
        large_up_actual = actual[large_up_mask]
        large_up_pred = predicted[large_up_mask]
        
        # 大跌情况
        large_down_mask = actual <= q10
        large_down_actual = actual[large_down_mask]
        large_down_pred = predicted[large_down_mask]
        
        print(f"\n{model_name} - 极值预测:")
        print(f"  大涨阈值: {q90:.6f}")
        print(f"  大跌阈值: {q10:.6f}")
        
        if len(large_up_actual) > 1:
            up_corr = large_up_actual.corr(large_up_pred)
            print(f"  大涨预测相关性: {up_corr:.4f}")
            print(f"  大涨真实均值: {large_up_actual.mean():.6f}")
            print(f"  大涨预测均值: {large_up_pred.mean():.6f}")
        
        if len(large_down_actual) > 1:
            down_corr = large_down_actual.corr(large_down_pred)
            print(f"  大跌预测相关性: {down_corr:.4f}")
            print(f"  大跌真实均值: {large_down_actual.mean():.6f}")
            print(f"  大跌预测均值: {large_down_pred.mean():.6f}")
    
    analyze_extreme_prediction(original_df, "原始模型")
    analyze_extreme_prediction(improved_df, "改进模型")
    
    # 绘制对比图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 散点图对比
    axes[0, 0].scatter(original_df['actual_return'], original_df['predicted_return'], 
                      alpha=0.6, s=20, label='原始模型', color='blue')
    axes[0, 0].plot([original_df['actual_return'].min(), original_df['actual_return'].max()],
                    [original_df['actual_return'].min(), original_df['actual_return'].max()],
                    'r--', lw=2)
    axes[0, 0].set_xlabel('真实收益率')
    axes[0, 0].set_ylabel('预测收益率')
    axes[0, 0].set_title(f'原始模型 (相关性: {original_stats["correlation"]:.4f})')
    axes[0, 0].grid(True, alpha=0.3)
    
    axes[0, 1].scatter(improved_df['actual_return'], improved_df['predicted_return'], 
                      alpha=0.6, s=20, label='改进模型', color='green')
    axes[0, 1].plot([improved_df['actual_return'].min(), improved_df['actual_return'].max()],
                    [improved_df['actual_return'].min(), improved_df['actual_return'].max()],
                    'r--', lw=2)
    axes[0, 1].set_xlabel('真实收益率')
    axes[0, 1].set_ylabel('预测收益率')
    axes[0, 1].set_title(f'改进模型 (相关性: {improved_stats["correlation"]:.4f})')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 2. 分布对比
    axes[0, 2].hist(original_df['actual_return'], bins=30, alpha=0.7, label='真实值', density=True)
    axes[0, 2].hist(original_df['predicted_return'], bins=30, alpha=0.7, label='原始预测', density=True)
    axes[0, 2].set_xlabel('收益率')
    axes[0, 2].set_ylabel('密度')
    axes[0, 2].set_title('原始模型分布对比')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 3. 改进模型分布对比
    axes[1, 0].hist(improved_df['actual_return'], bins=30, alpha=0.7, label='真实值', density=True)
    axes[1, 0].hist(improved_df['predicted_return'], bins=30, alpha=0.7, label='改进预测', density=True)
    axes[1, 0].set_xlabel('收益率')
    axes[1, 0].set_ylabel('密度')
    axes[1, 0].set_title('改进模型分布对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 预测范围对比
    models = ['原始模型', '改进模型']
    pred_stds = [original_stats['pred_std'], improved_stats['pred_std']]
    true_stds = [original_stats['true_std'], improved_stats['true_std']]
    
    x = np.arange(len(models))
    width = 0.35
    
    axes[1, 1].bar(x - width/2, pred_stds, width, label='预测标准差', alpha=0.8)
    axes[1, 1].bar(x + width/2, true_stds, width, label='真实标准差', alpha=0.8)
    axes[1, 1].set_xlabel('模型')
    axes[1, 1].set_ylabel('标准差')
    axes[1, 1].set_title('预测范围对比')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(models)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 5. 指标对比雷达图
    metrics = ['相关系数', 'R²', '标准差比率']
    original_values = [abs(original_stats['correlation']), 
                      max(0, original_stats['r2']), 
                      min(1, original_stats['std_ratio'])]
    improved_values = [abs(improved_stats['correlation']), 
                      max(0, improved_stats['r2']), 
                      min(1, improved_stats['std_ratio'])]
    
    x = np.arange(len(metrics))
    axes[1, 2].bar(x - width/2, original_values, width, label='原始模型', alpha=0.8)
    axes[1, 2].bar(x + width/2, improved_values, width, label='改进模型', alpha=0.8)
    axes[1, 2].set_xlabel('评估指标')
    axes[1, 2].set_ylabel('指标值')
    axes[1, 2].set_title('综合指标对比')
    axes[1, 2].set_xticks(x)
    axes[1, 2].set_xticklabels(metrics, rotation=45)
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 改进效果总结
    print("\n=== 改进效果总结 ===")
    
    correlation_improvement = improved_stats['correlation'] - original_stats['correlation']
    std_ratio_improvement = improved_stats['std_ratio'] - original_stats['std_ratio']
    r2_improvement = improved_stats['r2'] - original_stats['r2']
    
    print(f"相关系数改进: {correlation_improvement:+.4f}")
    print(f"标准差比率改进: {std_ratio_improvement:+.4f}")
    print(f"R²改进: {r2_improvement:+.4f}")
    
    if correlation_improvement > 0.05:
        print("✅ 相关性显著提升")
    elif correlation_improvement > 0.01:
        print("🔶 相关性略有提升")
    else:
        print("❌ 相关性无明显改进")
    
    if std_ratio_improvement > 0.2:
        print("✅ 预测范围显著扩大")
    elif std_ratio_improvement > 0.1:
        print("🔶 预测范围有所扩大")
    else:
        print("❌ 预测范围无明显改进")

if __name__ == "__main__":
    compare_prediction_results()
