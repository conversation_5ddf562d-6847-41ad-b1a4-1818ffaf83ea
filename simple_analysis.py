#!/usr/bin/env python3
"""
简单分析预测结果，不依赖外部库
"""

import csv
import math

def read_csv_data(filename):
    """读取CSV数据"""
    data = []
    try:
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append({
                    'date': row['date'],
                    'actual_return': float(row['actual_return']),
                    'predicted_return': float(row['predicted_return']),
                    'error': float(row['error']),
                    'abs_error': float(row['abs_error'])
                })
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return None
    return data

def calculate_stats(values):
    """计算基本统计量"""
    n = len(values)
    if n == 0:
        return {}
    
    mean_val = sum(values) / n
    variance = sum((x - mean_val) ** 2 for x in values) / n
    std_val = math.sqrt(variance)
    min_val = min(values)
    max_val = max(values)
    
    # 计算分位数
    sorted_vals = sorted(values)
    q25_idx = int(n * 0.25)
    q75_idx = int(n * 0.75)
    q10_idx = int(n * 0.10)
    q90_idx = int(n * 0.90)
    
    return {
        'mean': mean_val,
        'std': std_val,
        'min': min_val,
        'max': max_val,
        'q25': sorted_vals[q25_idx],
        'q75': sorted_vals[q75_idx],
        'q10': sorted_vals[q10_idx],
        'q90': sorted_vals[q90_idx]
    }

def calculate_correlation(x, y):
    """计算相关系数"""
    n = len(x)
    if n == 0:
        return 0
    
    mean_x = sum(x) / n
    mean_y = sum(y) / n
    
    numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    denominator_x = sum((x[i] - mean_x) ** 2 for i in range(n))
    denominator_y = sum((y[i] - mean_y) ** 2 for i in range(n))
    
    if denominator_x == 0 or denominator_y == 0:
        return 0
    
    return numerator / math.sqrt(denominator_x * denominator_y)

def analyze_prediction_results():
    """分析预测结果"""
    print("=== 预测结果分析 ===")
    
    # 读取数据
    data = read_csv_data('simple_prediction_results.csv')
    if data is None:
        return
    
    print(f"总样本数: {len(data)}")
    
    # 提取数据
    actual_returns = [d['actual_return'] for d in data]
    predicted_returns = [d['predicted_return'] for d in data]
    errors = [d['error'] for d in data]
    
    # 计算统计量
    actual_stats = calculate_stats(actual_returns)
    predicted_stats = calculate_stats(predicted_returns)
    
    print("\n=== 真实收益率统计 ===")
    print(f"均值: {actual_stats['mean']:.6f}")
    print(f"标准差: {actual_stats['std']:.6f}")
    print(f"最小值: {actual_stats['min']:.6f}")
    print(f"最大值: {actual_stats['max']:.6f}")
    print(f"25%分位数: {actual_stats['q25']:.6f}")
    print(f"75%分位数: {actual_stats['q75']:.6f}")
    print(f"10%分位数: {actual_stats['q10']:.6f}")
    print(f"90%分位数: {actual_stats['q90']:.6f}")
    
    print("\n=== 预测收益率统计 ===")
    print(f"均值: {predicted_stats['mean']:.6f}")
    print(f"标准差: {predicted_stats['std']:.6f}")
    print(f"最小值: {predicted_stats['min']:.6f}")
    print(f"最大值: {predicted_stats['max']:.6f}")
    print(f"25%分位数: {predicted_stats['q25']:.6f}")
    print(f"75%分位数: {predicted_stats['q75']:.6f}")
    print(f"10%分位数: {predicted_stats['q10']:.6f}")
    print(f"90%分位数: {predicted_stats['q90']:.6f}")
    
    # 计算相关性
    correlation = calculate_correlation(actual_returns, predicted_returns)
    print(f"\n=== 相关性分析 ===")
    print(f"预测值与真实值的相关系数: {correlation:.4f}")
    
    # 计算关键比率
    std_ratio = predicted_stats['std'] / actual_stats['std']
    print(f"预测标准差 / 真实标准差 = {std_ratio:.4f}")
    
    # 分析极值预测能力
    print("\n=== 极值预测能力分析 ===")
    
    large_up_threshold = actual_stats['q90']
    large_down_threshold = actual_stats['q10']
    
    print(f"大涨阈值 (90%分位数): {large_up_threshold:.6f}")
    print(f"大跌阈值 (10%分位数): {large_down_threshold:.6f}")
    
    # 大涨情况分析
    large_up_actual = [d['actual_return'] for d in data if d['actual_return'] >= large_up_threshold]
    large_up_pred = [d['predicted_return'] for d in data if d['actual_return'] >= large_up_threshold]
    
    if large_up_actual:
        large_up_actual_mean = sum(large_up_actual) / len(large_up_actual)
        large_up_pred_mean = sum(large_up_pred) / len(large_up_pred)
        large_up_corr = calculate_correlation(large_up_actual, large_up_pred)
        
        print(f"\n大涨情况 ({len(large_up_actual)}个样本):")
        print(f"  真实收益率均值: {large_up_actual_mean:.6f}")
        print(f"  预测收益率均值: {large_up_pred_mean:.6f}")
        print(f"  预测偏差: {(large_up_pred_mean - large_up_actual_mean):.6f}")
        print(f"  预测相关性: {large_up_corr:.4f}")
    
    # 大跌情况分析
    large_down_actual = [d['actual_return'] for d in data if d['actual_return'] <= large_down_threshold]
    large_down_pred = [d['predicted_return'] for d in data if d['actual_return'] <= large_down_threshold]
    
    if large_down_actual:
        large_down_actual_mean = sum(large_down_actual) / len(large_down_actual)
        large_down_pred_mean = sum(large_down_pred) / len(large_down_pred)
        large_down_corr = calculate_correlation(large_down_actual, large_down_pred)
        
        print(f"\n大跌情况 ({len(large_down_actual)}个样本):")
        print(f"  真实收益率均值: {large_down_actual_mean:.6f}")
        print(f"  预测收益率均值: {large_down_pred_mean:.6f}")
        print(f"  预测偏差: {(large_down_pred_mean - large_down_actual_mean):.6f}")
        print(f"  预测相关性: {large_down_corr:.4f}")
    
    # 问题诊断
    print("\n=== 问题诊断 ===")
    
    if std_ratio < 0.3:
        print("❌ 严重问题：预测值方差过小，模型过度趋于均值")
        print(f"   预测标准差只有真实标准差的 {std_ratio*100:.1f}%")
    elif std_ratio < 0.5:
        print("⚠️  中等问题：预测值方差偏小，模型倾向于保守预测")
        print(f"   预测标准差是真实标准差的 {std_ratio*100:.1f}%")
    elif std_ratio < 0.8:
        print("✅ 轻微问题：预测值方差略小，但基本可接受")
        print(f"   预测标准差是真实标准差的 {std_ratio*100:.1f}%")
    else:
        print("✅ 良好：预测值方差合理")
        print(f"   预测标准差是真实标准差的 {std_ratio*100:.1f}%")
    
    if abs(correlation) < 0.1:
        print("❌ 严重问题：预测值与真实值几乎无相关性")
    elif abs(correlation) < 0.3:
        print("⚠️  中等问题：预测值与真实值相关性较弱")
    elif abs(correlation) < 0.5:
        print("✅ 轻微问题：预测值与真实值有一定相关性")
    else:
        print("✅ 良好：预测值与真实值相关性较强")
    
    # 生成简单的文本图表
    print("\n=== 收益率分布对比 (文本图表) ===")
    
    # 创建分布区间
    min_val = min(min(actual_returns), min(predicted_returns))
    max_val = max(max(actual_returns), max(predicted_returns))
    
    bins = 20
    bin_width = (max_val - min_val) / bins
    
    print(f"收益率范围: [{min_val:.6f}, {max_val:.6f}]")
    print(f"区间宽度: {bin_width:.6f}")
    print()
    
    # 统计每个区间的数量
    actual_hist = [0] * bins
    pred_hist = [0] * bins
    
    for val in actual_returns:
        bin_idx = min(int((val - min_val) / bin_width), bins - 1)
        actual_hist[bin_idx] += 1
    
    for val in predicted_returns:
        bin_idx = min(int((val - min_val) / bin_width), bins - 1)
        pred_hist[bin_idx] += 1
    
    # 打印直方图
    max_count = max(max(actual_hist), max(pred_hist))
    scale = 50 / max_count if max_count > 0 else 1
    
    print("区间范围           真实值分布                    预测值分布")
    print("-" * 80)
    
    for i in range(bins):
        bin_start = min_val + i * bin_width
        bin_end = min_val + (i + 1) * bin_width
        
        actual_bar = "█" * int(actual_hist[i] * scale)
        pred_bar = "█" * int(pred_hist[i] * scale)
        
        print(f"[{bin_start:8.5f},{bin_end:8.5f}] {actual_bar:<25} {pred_bar:<25}")
    
    print("\n=== 建议改进措施 ===")
    print("1. 增加极值样本权重，让模型更关注大涨大跌")
    print("2. 调整模型参数，减少过度平滑")
    print("3. 使用分位数回归或其他损失函数")
    print("4. 增加波动率、极值指标等特征")
    print("5. 考虑集成学习方法")

if __name__ == "__main__":
    analyze_prediction_results()
