#!/usr/bin/env python3
"""
简单测试LightGBM回归模型预测准确率（不绘图）
"""

import sys
import os
import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

sys.path.append('./models/lightgbm')
from lgbm_regression import LightGBMStockRegressionModel

def simple_test():
    """简单测试预测准确率"""
    print("=== 简单测试预测准确率 ===")
    
    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'
    model_path = './model_lgbm_regression.pkl'
    
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"数据文件不存在: {csv_file}")
        return
    
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return
    
    try:
        # 创建模型并加载
        model = LightGBMStockRegressionModel()
        model.load_model(model_path)
        
        # 准备数据
        print("准备数据...")
        X, y, dates = model.prepare_data(csv_file, window=200)
        
        # 使用最后500个样本进行测试
        test_size = min(500, len(X))
        X_test = X[-test_size:]
        y_test = y[-test_size:]
        dates_test = dates[-test_size:]
        
        print(f"使用最后 {test_size} 个样本进行测试")
        print(f"测试时间范围: {dates_test[0]} 到 {dates_test[-1]}")
        
        # 进行预测
        print("进行预测...")
        predictions = model.predict(X_test)
        
        # 计算准确率指标
        mse = mean_squared_error(y_test, predictions)
        mae = mean_absolute_error(y_test, predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, predictions)
        
        print(f"\n=== 预测准确率指标 ===")
        print(f"RMSE (均方根误差): {rmse:.6f}")
        print(f"MAE (平均绝对误差): {mae:.6f}")
        print(f"R² (决定系数): {r2:.4f}")
        
        # 计算方向准确率
        pred_direction = np.sign(predictions)
        actual_direction = np.sign(y_test)
        direction_accuracy = np.mean(pred_direction == actual_direction)
        
        print(f"方向预测准确率: {direction_accuracy:.4f} ({direction_accuracy*100:.2f}%)")
        
        # 分析预测误差分布
        errors = predictions - y_test
        print(f"\n=== 预测误差分析 ===")
        print(f"误差均值: {np.mean(errors):.6f}")
        print(f"误差标准差: {np.std(errors):.6f}")
        print(f"误差最大值: {np.max(errors):.6f}")
        print(f"误差最小值: {np.min(errors):.6f}")
        
        # 计算不同误差范围的样本比例
        error_ranges = [0.001, 0.002, 0.005, 0.01]
        print(f"\n=== 误差范围分析 ===")
        for threshold in error_ranges:
            within_range = np.mean(np.abs(errors) <= threshold)
            print(f"误差在 ±{threshold:.3f} 内的样本比例: {within_range:.4f} ({within_range*100:.2f}%)")
        
        # 显示一些具体的预测示例
        print(f"\n=== 预测示例 (最后10个样本) ===")
        print("日期\t\t\t实际收益率\t预测收益率\t误差")
        print("-" * 70)
        for i in range(-10, 0):
            date = dates_test[i]
            actual = y_test[i]
            pred = predictions[i]
            error = pred - actual
            print(f"{date}\t{actual:.6f}\t{pred:.6f}\t{error:.6f}")
        
        print(f"\n=== 总结 ===")
        print(f"在最新的 {test_size} 个样本上:")
        print(f"• RMSE: {rmse:.6f} (越小越好)")
        print(f"• MAE: {mae:.6f} (越小越好)")
        print(f"• R²: {r2:.4f} (越接近1越好)")
        print(f"• 方向准确率: {direction_accuracy*100:.2f}% (随机猜测为50%)")
        
        # 评估模型表现
        if r2 > 0.1:
            performance = "较好"
        elif r2 > 0.05:
            performance = "一般"
        elif r2 > 0:
            performance = "较差"
        else:
            performance = "很差"
        
        print(f"• 模型表现评估: {performance}")
        
        if direction_accuracy > 0.55:
            direction_performance = "较好"
        elif direction_accuracy > 0.52:
            direction_performance = "一般"
        else:
            direction_performance = "较差"
        
        print(f"• 方向预测评估: {direction_performance}")
        
        # 保存结果到CSV
        results_df = pd.DataFrame({
            'date': dates_test,
            'actual_return': y_test,
            'predicted_return': predictions,
            'error': errors,
            'abs_error': np.abs(errors)
        })
        results_df.to_csv('./simple_prediction_results.csv', index=False)
        print(f"\n详细预测结果已保存到: ./simple_prediction_results.csv")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
