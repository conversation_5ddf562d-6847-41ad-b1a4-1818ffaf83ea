#!/usr/bin/env python3
"""
分析预测收益率趋于均值的问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_prediction_results():
    """分析预测结果，找出问题所在"""
    
    # 读取结果文件
    df = pd.read_csv('simple_prediction_results.csv')
    
    print("=== 预测结果分析 ===")
    print(f"总样本数: {len(df)}")
    
    # 基本统计
    print("\n=== 真实收益率统计 ===")
    print(f"均值: {df['actual_return'].mean():.6f}")
    print(f"标准差: {df['actual_return'].std():.6f}")
    print(f"最小值: {df['actual_return'].min():.6f}")
    print(f"最大值: {df['actual_return'].max():.6f}")
    print(f"25%分位数: {df['actual_return'].quantile(0.25):.6f}")
    print(f"75%分位数: {df['actual_return'].quantile(0.75):.6f}")
    
    print("\n=== 预测收益率统计 ===")
    print(f"均值: {df['predicted_return'].mean():.6f}")
    print(f"标准差: {df['predicted_return'].std():.6f}")
    print(f"最小值: {df['predicted_return'].min():.6f}")
    print(f"最大值: {df['predicted_return'].max():.6f}")
    print(f"25%分位数: {df['predicted_return'].quantile(0.25):.6f}")
    print(f"75%分位数: {df['predicted_return'].quantile(0.75):.6f}")
    
    # 计算相关性
    correlation = df['actual_return'].corr(df['predicted_return'])
    print(f"\n=== 相关性分析 ===")
    print(f"预测值与真实值的相关系数: {correlation:.4f}")
    
    # 分析极值预测能力
    print("\n=== 极值预测能力分析 ===")
    
    # 定义大涨大跌的阈值
    large_up_threshold = df['actual_return'].quantile(0.9)  # 前10%的大涨
    large_down_threshold = df['actual_return'].quantile(0.1)  # 后10%的大跌
    
    print(f"大涨阈值 (90%分位数): {large_up_threshold:.6f}")
    print(f"大跌阈值 (10%分位数): {large_down_threshold:.6f}")
    
    # 大涨情况下的预测
    large_up_mask = df['actual_return'] >= large_up_threshold
    large_up_actual = df.loc[large_up_mask, 'actual_return']
    large_up_pred = df.loc[large_up_mask, 'predicted_return']
    
    print(f"\n大涨情况 ({large_up_mask.sum()}个样本):")
    print(f"  真实收益率均值: {large_up_actual.mean():.6f}")
    print(f"  预测收益率均值: {large_up_pred.mean():.6f}")
    print(f"  预测偏差: {(large_up_pred.mean() - large_up_actual.mean()):.6f}")
    
    # 大跌情况下的预测
    large_down_mask = df['actual_return'] <= large_down_threshold
    large_down_actual = df.loc[large_down_mask, 'actual_return']
    large_down_pred = df.loc[large_down_mask, 'predicted_return']
    
    print(f"\n大跌情况 ({large_down_mask.sum()}个样本):")
    print(f"  真实收益率均值: {large_down_actual.mean():.6f}")
    print(f"  预测收益率均值: {large_down_pred.mean():.6f}")
    print(f"  预测偏差: {(large_down_pred.mean() - large_down_actual.mean()):.6f}")
    
    # 绘制分析图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 散点图
    axes[0, 0].scatter(df['actual_return'], df['predicted_return'], alpha=0.6, s=20)
    axes[0, 0].plot([df['actual_return'].min(), df['actual_return'].max()], 
                    [df['actual_return'].min(), df['actual_return'].max()], 'r--', lw=2)
    axes[0, 0].set_xlabel('真实收益率')
    axes[0, 0].set_ylabel('预测收益率')
    axes[0, 0].set_title(f'预测 vs 真实 (相关系数: {correlation:.4f})')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 分布对比
    axes[0, 1].hist(df['actual_return'], bins=50, alpha=0.7, label='真实收益率', density=True)
    axes[0, 1].hist(df['predicted_return'], bins=50, alpha=0.7, label='预测收益率', density=True)
    axes[0, 1].set_xlabel('收益率')
    axes[0, 1].set_ylabel('密度')
    axes[0, 1].set_title('收益率分布对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 时间序列对比
    axes[1, 0].plot(df['actual_return'], label='真实收益率', alpha=0.8, linewidth=1)
    axes[1, 0].plot(df['predicted_return'], label='预测收益率', alpha=0.8, linewidth=1)
    axes[1, 0].set_xlabel('时间序列索引')
    axes[1, 0].set_ylabel('收益率')
    axes[1, 0].set_title('时间序列对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 残差分析
    residuals = df['predicted_return'] - df['actual_return']
    axes[1, 1].scatter(df['predicted_return'], residuals, alpha=0.6, s=20)
    axes[1, 1].axhline(y=0, color='r', linestyle='--', lw=2)
    axes[1, 1].set_xlabel('预测收益率')
    axes[1, 1].set_ylabel('残差 (预测-真实)')
    axes[1, 1].set_title('残差分析')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('prediction_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 问题诊断
    print("\n=== 问题诊断 ===")
    pred_std = df['predicted_return'].std()
    actual_std = df['actual_return'].std()
    std_ratio = pred_std / actual_std
    
    print(f"预测值标准差 / 真实值标准差 = {std_ratio:.4f}")
    
    if std_ratio < 0.3:
        print("❌ 严重问题：预测值方差过小，模型过度趋于均值")
    elif std_ratio < 0.5:
        print("⚠️  中等问题：预测值方差偏小，模型倾向于保守预测")
    elif std_ratio < 0.8:
        print("✅ 轻微问题：预测值方差略小，但基本可接受")
    else:
        print("✅ 良好：预测值方差合理")
    
    if abs(correlation) < 0.1:
        print("❌ 严重问题：预测值与真实值几乎无相关性")
    elif abs(correlation) < 0.3:
        print("⚠️  中等问题：预测值与真实值相关性较弱")
    elif abs(correlation) < 0.5:
        print("✅ 轻微问题：预测值与真实值有一定相关性")
    else:
        print("✅ 良好：预测值与真实值相关性较强")

if __name__ == "__main__":
    analyze_prediction_results()
