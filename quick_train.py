#!/usr/bin/env python3
"""
快速训练LightGBM回归模型
"""

import sys
import os
sys.path.append('./models/lightgbm')

from lgbm_regression import LightGBMStockRegressionModel

def quick_train():
    """快速训练模型"""
    print("=== 快速训练LightGBM回归模型 ===")
    
    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'
    model_path = './model_lgbm_regression.pkl'
    
    # 检查数据文件
    if not os.path.exists(csv_file):
        print(f"数据文件不存在: {csv_file}")
        return
    
    try:
        # 创建模型
        model = LightGBMStockRegressionModel(
            batch_size=200,
            validation_size=50,
            model_params={
                'num_leaves': 15,
                'learning_rate': 0.1,
                'n_estimators': 30,
                'random_state': 42
            }
        )
        
        # 准备数据
        print("准备数据...")
        X, y, dates = model.prepare_data(csv_file, window=200)
        
        # 使用最后2000个样本进行快速训练
        train_size = min(2000, len(X) - 500)  # 保留500个样本用于测试
        X_train = X[:train_size]
        y_train = y[:train_size]
        
        X_val = X[train_size:train_size+200]
        y_val = y[train_size:train_size+200]
        
        print(f"训练集大小: {X_train.shape}")
        print(f"验证集大小: {X_val.shape}")
        
        # 训练模型
        print("开始训练...")
        result = model.train_model(X_train, y_train, X_val, y_val)
        
        print("训练完成!")
        print(f"训练集 RMSE: {result['train_rmse']:.6f}")
        print(f"验证集 RMSE: {result['val_rmse']:.6f}")
        print(f"验证集 R²: {result['val_r2']:.4f}")
        
        # 保存模型
        model.save_model(model_path)
        print(f"模型已保存到: {model_path}")
        
        return True
        
    except Exception as e:
        print(f"训练出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_train()
    if success:
        print("快速训练成功完成!")
    else:
        print("快速训练失败!")
