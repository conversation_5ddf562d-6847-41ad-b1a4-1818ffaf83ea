#!/usr/bin/env python3
"""
LightGBM回归模型使用示例
预测未来10根K线的平均收益率
"""

import sys
import os
sys.path.append('./models/lightgbm')

from lgbm_regression import LightGBMStockRegressionModel, run_lgbm_regression

def main():
    """主函数：演示LightGBM回归模型的使用"""
    print("=== LightGBM回归模型使用示例 ===")
    print("预测未来10根K线的平均收益率")
    
    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'  # 数据文件
    model_path = './model_lgbm_regression.pkl'  # 模型保存路径
    batch_size = 500  # 每次训练样本数
    validation_size = 100  # 每次验证样本数
    
    # 自定义LightGBM参数
    custom_params = {
        'num_leaves': 31,
        'learning_rate': 0.05,
        'n_estimators': 100,
        'random_state': 42
    }
    
    try:
        # 检查数据文件是否存在
        if not os.path.exists(csv_file):
            print(f"数据文件不存在: {csv_file}")
            print("请确保数据文件存在或修改csv_file路径")
            return
        
        # 运行模型
        print("开始运行回归模型...")
        results = run_lgbm_regression(
            csv_file=csv_file,
            model_path=model_path,
            batch_size=batch_size,
            validation_size=validation_size,
            custom_params=custom_params
        )
        
        # 创建模型实例用于后续操作
        model = LightGBMStockRegressionModel(
            batch_size=batch_size,
            validation_size=validation_size,
            model_params=custom_params
        )
        
        # 加载模型
        model.load_model(model_path)
        
        # 绘制结果
        if 'validation_results' in results:  # 滚动验证结果
            print("绘制滚动验证结果...")
            model.plot_results(results, save_path='./lgbm_regression_results.png')
        else:  # 测试结果
            print("绘制测试结果...")
            # 为测试结果创建简化的结果字典
            test_results = {
                'predictions': results['predictions'].tolist() if hasattr(results['predictions'], 'tolist') else results['predictions'],
                'true_values': results['true_values'].tolist() if hasattr(results['true_values'], 'tolist') else results['true_values'],
                'dates': results['dates'],
                'overall_r2': results['r2'],
                'validation_results': []  # 空的验证结果
            }
            model.plot_results(test_results, save_path='./lgbm_regression_test_results.png')
        
        # 显示特征重要性
        print("\n=== 特征重要性 (Top 10) ===")
        feature_imp = model.get_feature_importance(top_n=10)
        print(feature_imp)
        
        # 保存详细结果
        if 'validation_results' in results:
            import pandas as pd
            results_df = pd.DataFrame(results['validation_results'])
            results_df.to_csv('./lgbm_regression_validation_results.csv', index=False)
            print("详细结果已保存到: ./lgbm_regression_validation_results.csv")
            
            print(f"\n=== 最终结果 ===")
            print(f"总体 RMSE: {results['overall_rmse']:.6f}")
            print(f"总体 MAE: {results['overall_mae']:.6f}")
            print(f"总体 R²: {results['overall_r2']:.4f}")
            print(f"总验证样本数: {len(results['predictions'])}")
            print(f"总训练轮次: {results['total_rounds']}")
        else:
            print(f"\n=== 测试结果 ===")
            print(f"RMSE: {results['rmse']:.6f}")
            print(f"MAE: {results['mae']:.6f}")
            print(f"R²: {results['r2']:.4f}")
            print(f"测试样本数: {len(results['predictions'])}")
        
        # 演示单次预测
        print(f"\n=== 单次预测演示 ===")
        # 准备数据
        X, y, dates = model.prepare_data(csv_file, window=200)
        
        # 使用最后一个样本进行预测
        last_sample = X[-1:] 
        actual_return = y[-1]
        predicted_return = model.predict_single(last_sample)
        
        print(f"最后一个样本:")
        print(f"  日期: {dates[-1]}")
        print(f"  实际收益率: {actual_return:.6f}")
        print(f"  预测收益率: {predicted_return:.6f}")
        print(f"  预测误差: {abs(predicted_return - actual_return):.6f}")
        
        # 解释预测结果
        if predicted_return > 0.002:
            prediction_desc = "强烈看涨"
        elif predicted_return > 0.001:
            prediction_desc = "看涨"
        elif predicted_return > -0.001:
            prediction_desc = "震荡"
        elif predicted_return > -0.002:
            prediction_desc = "看跌"
        else:
            prediction_desc = "强烈看跌"
        
        print(f"  预测解读: {prediction_desc}")
        
        print("\n=== 模型使用完成 ===")
        
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
