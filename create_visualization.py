#!/usr/bin/env python3
"""
创建收益率对比的可视化图表
"""

import csv
import math

def read_csv_data(filename):
    """读取CSV数据"""
    data = []
    try:
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append({
                    'date': row['date'],
                    'actual_return': float(row['actual_return']),
                    'predicted_return': float(row['predicted_return']),
                    'error': float(row['error']),
                    'abs_error': float(row['abs_error'])
                })
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return None
    return data

def create_ascii_scatter_plot(x_data, y_data, title, x_label, y_label, width=80, height=25):
    """创建ASCII散点图"""
    print(f"\n=== {title} ===")
    
    if not x_data or not y_data:
        print("无数据")
        return
    
    # 计算数据范围
    x_min, x_max = min(x_data), max(x_data)
    y_min, y_max = min(y_data), max(y_data)
    
    # 添加边距
    x_range = x_max - x_min
    y_range = y_max - y_min
    
    if x_range == 0:
        x_range = 1
    if y_range == 0:
        y_range = 1
    
    x_min -= x_range * 0.1
    x_max += x_range * 0.1
    y_min -= y_range * 0.1
    y_max += y_range * 0.1
    
    # 创建画布
    canvas = [[' ' for _ in range(width)] for _ in range(height)]
    
    # 绘制数据点
    for i in range(len(x_data)):
        x_pos = int((x_data[i] - x_min) / (x_max - x_min) * (width - 1))
        y_pos = int((y_data[i] - y_min) / (y_max - y_min) * (height - 1))
        
        # 翻转Y轴（因为数组索引从上到下）
        y_pos = height - 1 - y_pos
        
        if 0 <= x_pos < width and 0 <= y_pos < height:
            canvas[y_pos][x_pos] = '●'
    
    # 绘制完美预测线 (y=x)
    if x_min <= y_max and x_max >= y_min:  # 线与图表有交集
        for x_pos in range(width):
            x_val = x_min + (x_pos / (width - 1)) * (x_max - x_min)
            y_val = x_val  # y = x
            
            if y_min <= y_val <= y_max:
                y_pos = int((y_val - y_min) / (y_max - y_min) * (height - 1))
                y_pos = height - 1 - y_pos
                
                if 0 <= y_pos < height and canvas[y_pos][x_pos] == ' ':
                    canvas[y_pos][x_pos] = '─'
    
    # 打印图表
    print(f"{y_label}")
    print("│")
    
    for row in canvas:
        print("│" + "".join(row))
    
    print("└" + "─" * width)
    print(" " * (width//2 - len(x_label)//2) + x_label)
    
    # 打印图例和统计信息
    print(f"\n图例: ● 数据点  ─ 完美预测线 (y=x)")
    print(f"X轴范围: [{x_min:.6f}, {x_max:.6f}]")
    print(f"Y轴范围: [{y_min:.6f}, {y_max:.6f}]")

def create_time_series_plot(dates, actual_data, pred_data, title, width=100, height=20):
    """创建时间序列ASCII图"""
    print(f"\n=== {title} ===")
    
    if not actual_data or not pred_data:
        print("无数据")
        return
    
    # 计算数据范围
    all_data = actual_data + pred_data
    y_min, y_max = min(all_data), max(all_data)
    
    # 添加边距
    y_range = y_max - y_min
    if y_range == 0:
        y_range = 1
    
    y_min -= y_range * 0.1
    y_max += y_range * 0.1
    
    # 创建画布
    canvas = [[' ' for _ in range(width)] for _ in range(height)]
    
    # 绘制真实值
    for i in range(len(actual_data)):
        x_pos = int(i / (len(actual_data) - 1) * (width - 1)) if len(actual_data) > 1 else 0
        y_pos = int((actual_data[i] - y_min) / (y_max - y_min) * (height - 1))
        y_pos = height - 1 - y_pos
        
        if 0 <= x_pos < width and 0 <= y_pos < height:
            canvas[y_pos][x_pos] = '●'
    
    # 绘制预测值
    for i in range(len(pred_data)):
        x_pos = int(i / (len(pred_data) - 1) * (width - 1)) if len(pred_data) > 1 else 0
        y_pos = int((pred_data[i] - y_min) / (y_max - y_min) * (height - 1))
        y_pos = height - 1 - y_pos
        
        if 0 <= x_pos < width and 0 <= y_pos < height:
            if canvas[y_pos][x_pos] == ' ':
                canvas[y_pos][x_pos] = '○'
            else:
                canvas[y_pos][x_pos] = '◉'  # 重叠点
    
    # 绘制零线
    if y_min <= 0 <= y_max:
        zero_y_pos = int((0 - y_min) / (y_max - y_min) * (height - 1))
        zero_y_pos = height - 1 - zero_y_pos
        
        for x_pos in range(width):
            if canvas[zero_y_pos][x_pos] == ' ':
                canvas[zero_y_pos][x_pos] = '─'
    
    # 打印图表
    print("收益率")
    print("│")
    
    for row in canvas:
        print("│" + "".join(row))
    
    print("└" + "─" * width)
    print(" " * (width//2 - 2) + "时间")
    
    # 打印图例
    print(f"\n图例: ● 真实收益率  ○ 预测收益率  ◉ 重叠点  ─ 零线")
    print(f"Y轴范围: [{y_min:.6f}, {y_max:.6f}]")

def main():
    """主函数"""
    print("=== 收益率预测可视化分析 ===")
    
    # 读取数据
    data = read_csv_data('simple_prediction_results.csv')
    if data is None:
        return
    
    # 提取数据
    actual_returns = [d['actual_return'] for d in data]
    predicted_returns = [d['predicted_return'] for d in data]
    dates = [d['date'] for d in data]
    
    # 1. 散点图：预测 vs 真实
    create_ascii_scatter_plot(
        actual_returns, predicted_returns,
        "预测收益率 vs 真实收益率 散点图",
        "真实收益率", "预测收益率"
    )
    
    # 2. 时间序列图
    create_time_series_plot(
        dates, actual_returns, predicted_returns,
        "收益率时间序列对比"
    )
    
    # 3. 分布对比（更详细的直方图）
    print("\n=== 详细分布对比 ===")
    
    # 计算统计量
    actual_mean = sum(actual_returns) / len(actual_returns)
    actual_std = math.sqrt(sum((x - actual_mean) ** 2 for x in actual_returns) / len(actual_returns))
    
    pred_mean = sum(predicted_returns) / len(predicted_returns)
    pred_std = math.sqrt(sum((x - pred_mean) ** 2 for x in predicted_returns) / len(predicted_returns))
    
    print(f"真实收益率: 均值={actual_mean:.6f}, 标准差={actual_std:.6f}")
    print(f"预测收益率: 均值={pred_mean:.6f}, 标准差={pred_std:.6f}")
    print(f"标准差比率: {pred_std/actual_std:.4f}")
    
    # 4. 极值分析可视化
    print("\n=== 极值情况可视化 ===")
    
    # 计算分位数
    sorted_actual = sorted(actual_returns)
    n = len(sorted_actual)
    q10_idx = int(n * 0.1)
    q90_idx = int(n * 0.9)
    
    q10_threshold = sorted_actual[q10_idx]
    q90_threshold = sorted_actual[q90_idx]
    
    print(f"大跌阈值 (10%分位数): {q10_threshold:.6f}")
    print(f"大涨阈值 (90%分位数): {q90_threshold:.6f}")
    
    # 分析极值预测
    extreme_cases = []
    for i, d in enumerate(data):
        if d['actual_return'] <= q10_threshold or d['actual_return'] >= q90_threshold:
            case_type = "大涨" if d['actual_return'] >= q90_threshold else "大跌"
            extreme_cases.append({
                'type': case_type,
                'actual': d['actual_return'],
                'predicted': d['predicted_return'],
                'error': d['error']
            })
    
    print(f"\n极值案例分析 (共{len(extreme_cases)}个):")
    print("类型   真实收益率    预测收益率    预测误差")
    print("-" * 45)
    
    for case in extreme_cases[:10]:  # 只显示前10个
        print(f"{case['type']}   {case['actual']:10.6f}  {case['predicted']:10.6f}  {case['error']:10.6f}")
    
    if len(extreme_cases) > 10:
        print(f"... 还有 {len(extreme_cases) - 10} 个极值案例")
    
    # 5. 问题总结
    print("\n" + "="*60)
    print("🔍 问题总结")
    print("="*60)
    
    std_ratio = pred_std / actual_std
    correlation = sum((actual_returns[i] - actual_mean) * (predicted_returns[i] - pred_mean) 
                     for i in range(len(actual_returns))) / (len(actual_returns) * actual_std * pred_std)
    
    print(f"1. 预测范围过窄: 预测标准差只有真实标准差的 {std_ratio*100:.1f}%")
    print(f"2. 相关性较弱: 相关系数为 {correlation:.4f}")
    print(f"3. 极值预测失效: 大涨大跌时预测值仍接近0")
    print(f"4. 模型过于保守: 预测值集中在 [{min(predicted_returns):.6f}, {max(predicted_returns):.6f}]")
    print(f"5. 真实值范围: [{min(actual_returns):.6f}, {max(actual_returns):.6f}]")
    
    print("\n💡 建议解决方案:")
    print("1. 使用样本权重，给极值样本更高权重")
    print("2. 调整模型参数，减少过度平滑")
    print("3. 尝试分位数回归或其他损失函数")
    print("4. 增加波动率、极值相关特征")
    print("5. 考虑集成学习或多模型融合")

if __name__ == "__main__":
    main()
