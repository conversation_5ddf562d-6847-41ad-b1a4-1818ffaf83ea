"""
LightGBM回归滚动训练验证模型
基于lgbm_classify改造，主要特点：
1. 使用LightGBM回归模型预测未来10根K线的平均收益率
2. seq_len=1，输入X降维到(batch_size, features)
3. 滚动训练验证，每次只训练前batch_size个，验证后面n个后，继续滚动训练
4. 支持实盘定期重新训练
"""

import numpy as np
import pandas as pd
import lightgbm as lgb
from sklearn.metrics import accuracy_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
import os
import time

from typing import Tuple, List, Optional, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# 导入trmV2的特征工程函数
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trm.trmV2 import engineer_features_X, rolling_normalize


class LightGBMStockModel:
    """
    LightGBM股票预测模型，支持滚动训练验证
    """

    def __init__(self,
                 batch_size: int = 500,
                 validation_size: int = 100,
                 retrain_frequency: int = 50,
                 model_params: Optional[Dict[str, Any]] = None):
        """
        初始化LightGBM模型

        Args:
            batch_size: 每次训练的样本数量
            validation_size: 每次验证的样本数量
            retrain_frequency: 重新训练的频率（每多少次预测后重新训练）
            model_params: LightGBM模型参数
        """
        self.batch_size = batch_size
        self.validation_size = validation_size
        self.retrain_frequency = retrain_frequency
        self.prediction_count = 0  # 预测次数计数器

        # 默认LightGBM参数
        self.default_params = {
            'objective': 'multiclass',
            'num_class': 3,
            'metric': 'multi_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42,
            'n_estimators': 100,
            'early_stopping_rounds': 10
        }

        # 更新参数
        if model_params:
            self.default_params.update(model_params)

        self.model = None
        self.feature_names = None
        self.training_history = []  # 训练历史记录
        self.validation_history = []  # 验证历史记录

    def prepare_data(self, csv_file: str, window: int = 200, balance_classes: bool = True) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        准备训练数据

        Args:
            csv_file: CSV文件路径
            window: 滚动标准化窗口大小
            balance_classes: 是否进行类别平衡，默认True

        Returns:
            X: 特征数据 [样本数, 特征数]
            y: 目标标签 [样本数]
            dates: 对应的日期列表
        """
        print(f"加载数据: {csv_file}")

        # 1. 读取数据
        df = pd.read_csv(csv_file)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)
        print(f"原始数据形状: {df.shape}")

        # 2. 特征工程
        print("开始特征工程...")
        features_df = engineer_features_X(df)
        print(f"特征工程完成，特征数量: {features_df.shape[1]}")

        # 3. 创建目标变量（与trmV2保持一致）
        future_return_1d = df['close'].shift(-1) / df['close'] - 1
        future_return_2d = df['close'].shift(-2) / df['close'] - 1
        future_return_3d = df['close'].shift(-3) / df['close'] - 1
        future_return_avg = (future_return_1d + future_return_2d + future_return_3d) / 3

        # 使用绝对阈值创建标签
        threshold = 0.001
        def assign_label_absolute(return_val):
            if pd.isna(return_val):
                return np.nan
            elif return_val <= -threshold:
                return 0  # 跌
            elif return_val >= threshold:
                return 2  # 涨
            else:
                return 1  # 平

        features_df['target'] = future_return_avg.apply(assign_label_absolute)

        # 4. 清理数据
        features_clean = features_df.dropna()
        print(f"清理后数据形状: {features_clean.shape}")

        # 5. 分离特征和目标
        X_features = features_clean.iloc[:, :-1].values  # 除了最后一列target
        y_target = features_clean.iloc[:, -1].values

        # 6. 滚动标准化
        print("开始滚动标准化...")
        X_normalized, valid_start = rolling_normalize(X_features, window=window)
        y_target_valid = y_target[valid_start:]
        print(f"标准化完成，有效数据形状: X={X_normalized.shape}, y={y_target_valid.shape}")

        # 7. 获取对应的日期
        valid_dates = df['datetime'].iloc[len(df) - len(features_clean) + valid_start:].tolist()

        # 8. 保存特征名称
        self.feature_names = features_clean.columns[:-1].tolist()

        # 统计类别分布
        unique, counts = np.unique(np.array(y_target_valid), return_counts=True)
        total = len(y_target_valid)
        print("原始目标分布:")
        class_names = ['跌', '平', '涨']
        for label, count in zip(unique, counts):
            pct = count / total * 100
            print(f"  类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")

        # 类别平衡（可选）
        if balance_classes:
            X_normalized, y_target_valid, valid_dates = self.balance_classes(X_normalized, y_target_valid, valid_dates)

        return X_normalized, y_target_valid.astype(int), valid_dates

    def balance_classes(self, X: np.ndarray, y: np.ndarray, dates: List[str]) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        平衡类别数量，以最少的类别数量为准，其他类别随机抽样到相同数量

        Args:
            X: 特征数据
            y: 标签数据
            dates: 日期列表

        Returns:
            平衡后的X, y, dates
        """
        print("\n=== 开始类别平衡 ===")

        # 统计各类别数量
        unique, counts = np.unique(y, return_counts=True)
        class_names = ['跌', '平', '涨']

        print("平衡前类别分布:")
        for label, count in zip(unique, counts):
            pct = count / len(y) * 100
            print(f"  类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")

        # 找到最少的类别数量
        min_count = np.min(counts)
        print(f"\n以最少类别数量为准: {min_count} 样本")

        # 为每个类别随机抽样
        balanced_indices = []
        np.random.seed(42)  # 设置随机种子保证可重复性

        for class_label in unique:
            # 找到该类别的所有索引
            class_indices = np.where(y == class_label)[0]

            if len(class_indices) > min_count:
                # 如果该类别样本数超过最小值，随机抽样
                sampled_indices = np.random.choice(class_indices, size=min_count, replace=False)
                balanced_indices.extend(sampled_indices)
                print(f"  类别{int(class_label)} ({class_names[int(class_label)]}): 从{len(class_indices)}个样本中抽取{min_count}个")
            else:
                # 如果该类别样本数等于最小值，全部保留
                balanced_indices.extend(class_indices)
                print(f"  类别{int(class_label)} ({class_names[int(class_label)]}): 保留全部{len(class_indices)}个样本")

        # 排序索引以保持时间顺序
        balanced_indices = sorted(balanced_indices)

        # 提取平衡后的数据
        X_balanced = X[balanced_indices]
        y_balanced = y[balanced_indices]
        dates_balanced = [dates[i] for i in balanced_indices]

        # 验证平衡结果
        unique_balanced, counts_balanced = np.unique(y_balanced, return_counts=True)
        print("\n平衡后类别分布:")
        total_balanced = len(y_balanced)
        for label, count in zip(unique_balanced, counts_balanced):
            pct = count / total_balanced * 100
            print(f"  类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")

        print(f"总样本数: {len(y)} -> {len(y_balanced)}")
        print("=== 类别平衡完成 ===\n")

        return X_balanced, y_balanced, dates_balanced

    def train_model(self, X_train: np.ndarray, y_train: np.ndarray,
                   X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练LightGBM模型

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征（可选）
            y_val: 验证标签（可选）

        Returns:
            训练结果字典
        """
        print(f"训练模型，训练集大小: {X_train.shape}, 标签分布: {np.bincount(y_train)}")

        # 创建LightGBM数据集
        if self.feature_names is not None:
            train_data = lgb.Dataset(X_train, label=y_train, feature_name=self.feature_names)
        else:
            train_data = lgb.Dataset(X_train, label=y_train)

        valid_sets = [train_data]
        valid_names = ['train']

        if X_val is not None and y_val is not None:
            if self.feature_names is not None:
                val_data = lgb.Dataset(X_val, label=y_val, feature_name=self.feature_names, reference=train_data)
            else:
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            valid_sets.append(val_data)
            valid_names.append('valid')
            print(f"验证集大小: {X_val.shape}, 标签分布: {np.bincount(y_val)}")

        # 训练模型
        start_time = time.time()
        self.model = lgb.train(
            self.default_params,
            train_data,
            valid_sets=valid_sets,
            valid_names=valid_names,
            callbacks=[lgb.log_evaluation(0)]  # 不打印训练日志
        )
        training_time = time.time() - start_time

        # 计算训练准确率
        train_pred = self.model.predict(X_train)
        train_pred = np.array(train_pred)  # 确保是numpy数组
        train_pred_class = np.argmax(train_pred, axis=1)
        train_accuracy = accuracy_score(y_train, train_pred_class)

        result = {
            'training_time': training_time,
            'train_accuracy': train_accuracy,
            'train_samples': len(X_train)
        }

        # 计算验证准确率
        if X_val is not None and y_val is not None:
            val_pred = self.model.predict(X_val)
            val_pred = np.array(val_pred)  # 确保是numpy数组
            val_pred_class = np.argmax(val_pred, axis=1)
            val_accuracy = accuracy_score(y_val, val_pred_class)
            result['val_accuracy'] = val_accuracy
            result['val_samples'] = len(X_val)
            print(f"训练完成，用时: {training_time:.2f}s, 训练准确率: {train_accuracy:.3f}, 验证准确率: {val_accuracy:.3f}")
        else:
            print(f"训练完成，用时: {training_time:.2f}s, 训练准确率: {train_accuracy:.3f}")

        return result

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测

        Args:
            X: 输入特征

        Returns:
            预测类别, 预测概率
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train_model方法")

        pred_probs = self.model.predict(X)
        pred_probs = np.array(pred_probs)  # 确保是numpy数组
        pred_classes = np.argmax(pred_probs, axis=1)

        return pred_classes, pred_probs

    def rolling_train_validate(self, X: np.ndarray, y: np.ndarray, dates: List[str]) -> Dict[str, Any]:
        """
        滚动训练验证

        Args:
            X: 全部特征数据
            y: 全部标签数据
            dates: 对应的日期列表

        Returns:
            滚动验证结果
        """
        print(f"开始滚动训练验证，数据总量: {len(X)}")
        print(f"批次大小: {self.batch_size}, 验证大小: {self.validation_size}")

        total_samples = len(X)
        if total_samples < self.batch_size + self.validation_size:
            raise ValueError(f"数据量不足，需要至少 {self.batch_size + self.validation_size} 个样本")

        # 存储结果
        all_predictions = []
        all_true_labels = []
        all_prediction_probs = []
        all_dates = []
        validation_results = []

        # 计算滚动窗口数量
        max_start_idx = total_samples - self.batch_size - self.validation_size
        step_size = max(1, self.validation_size // 2)  # 每次滑动验证集大小的一半

        current_idx = 0
        round_num = 0

        while current_idx <= max_start_idx:
            round_num += 1
            print(f"\n=== 第 {round_num} 轮滚动训练验证 ===")

            # 定义训练和验证数据范围
            train_start = current_idx
            train_end = current_idx + self.batch_size
            val_start = train_end
            val_end = val_start + self.validation_size

            print(f"训练范围: [{train_start}:{train_end}], 验证范围: [{val_start}:{val_end}]")
            print(f"训练日期: {dates[train_start]} 到 {dates[train_end-1]}")
            print(f"验证日期: {dates[val_start]} 到 {dates[val_end-1]}")

            # 准备训练和验证数据
            X_train = X[train_start:train_end]
            y_train = y[train_start:train_end]
            X_val = X[val_start:val_end]
            y_val = y[val_start:val_end]

            # 训练模型
            train_result = self.train_model(X_train, y_train, X_val, y_val)

            # 验证预测
            val_pred_classes, val_pred_probs = self.predict(X_val)
            val_accuracy = accuracy_score(y_val, val_pred_classes)

            # 保存结果
            all_predictions.extend(val_pred_classes)
            all_true_labels.extend(y_val)
            all_prediction_probs.extend(val_pred_probs)
            all_dates.extend(dates[val_start:val_end])

            # 记录验证结果
            validation_result = {
                'round': round_num,
                'train_start': train_start,
                'train_end': train_end,
                'val_start': val_start,
                'val_end': val_end,
                'val_accuracy': val_accuracy,
                'train_date_start': dates[train_start],
                'train_date_end': dates[train_end-1],
                'val_date_start': dates[val_start],
                'val_date_end': dates[val_end-1]
            }
            validation_result.update(train_result)
            validation_results.append(validation_result)

            print(f"验证准确率: {val_accuracy:.3f}")

            # 移动到下一个窗口
            current_idx += step_size

            # 如果剩余数据不足一个完整的验证集，则处理剩余数据
            if current_idx + self.batch_size + self.validation_size > total_samples:
                remaining = total_samples - current_idx - self.batch_size
                if remaining > 0:
                    print(f"处理剩余 {remaining} 个样本...")
                    val_start = current_idx + self.batch_size
                    val_end = total_samples

                    X_train = X[current_idx:current_idx + self.batch_size]
                    y_train = y[current_idx:current_idx + self.batch_size]
                    X_val = X[val_start:val_end]
                    y_val = y[val_start:val_end]

                    train_result = self.train_model(X_train, y_train, X_val, y_val)
                    val_pred_classes, val_pred_probs = self.predict(X_val)
                    val_accuracy = accuracy_score(y_val, val_pred_classes)

                    all_predictions.extend(val_pred_classes)
                    all_true_labels.extend(y_val)
                    all_prediction_probs.extend(val_pred_probs)
                    all_dates.extend(dates[val_start:val_end])

                    round_num += 1
                    validation_result = {
                        'round': round_num,
                        'train_start': current_idx,
                        'train_end': current_idx + self.batch_size,
                        'val_start': val_start,
                        'val_end': val_end,
                        'val_accuracy': val_accuracy,
                        'train_date_start': dates[current_idx],
                        'train_date_end': dates[current_idx + self.batch_size - 1],
                        'val_date_start': dates[val_start],
                        'val_date_end': dates[val_end-1]
                    }
                    validation_result.update(train_result)
                    validation_results.append(validation_result)

                break

        # 计算总体结果
        overall_accuracy = accuracy_score(all_true_labels, all_predictions)

        print(f"\n=== 滚动验证完成 ===")
        print(f"总验证样本数: {len(all_predictions)}")
        print(f"总体准确率: {overall_accuracy:.3f}")

        # 分类别准确率
        class_names = ['跌', '平', '涨']
        for i in range(3):
            mask = np.array(all_true_labels) == i
            if np.sum(mask) > 0:
                class_acc = accuracy_score(np.array(all_true_labels)[mask], np.array(all_predictions)[mask])
                print(f"{class_names[i]}类准确率: {class_acc:.3f} ({np.sum(mask)}个样本)")

        return {
            'overall_accuracy': overall_accuracy,
            'predictions': all_predictions,
            'true_labels': all_true_labels,
            'prediction_probs': all_prediction_probs,
            'dates': all_dates,
            'validation_results': validation_results,
            'total_rounds': round_num
        }

    def save_model(self, filepath: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型尚未训练，无法保存")

        model_data = {
            'model': self.model,
            'feature_names': self.feature_names,
            'batch_size': self.batch_size,
            'validation_size': self.validation_size,
            'retrain_frequency': self.retrain_frequency,
            'default_params': self.default_params,
            'prediction_count': self.prediction_count,
            'training_history': self.training_history,
            'validation_history': self.validation_history
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        print(f"模型已保存到: {filepath}")

    def load_model(self, filepath: str):
        """加载模型"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"模型文件不存在: {filepath}")

        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)

        self.model = model_data['model']
        self.feature_names = model_data['feature_names']
        self.batch_size = model_data['batch_size']
        self.validation_size = model_data['validation_size']
        self.retrain_frequency = model_data['retrain_frequency']
        self.default_params = model_data['default_params']
        self.prediction_count = model_data.get('prediction_count', 0)
        self.training_history = model_data.get('training_history', [])
        self.validation_history = model_data.get('validation_history', [])

        print(f"模型已从 {filepath} 加载")

    def predict_single(self, X: np.ndarray, retrain_data: Optional[Tuple[np.ndarray, np.ndarray]] = None) -> Tuple[int, np.ndarray]:
        """
        单次预测（实盘使用）

        Args:
            X: 单个样本特征 [1, features] 或 [features]
            retrain_data: 重新训练数据 (X_retrain, y_retrain)，可选

        Returns:
            预测类别, 预测概率
        """
        if self.model is None:
            raise ValueError("模型尚未训练或加载")

        # 确保输入是2D数组
        if X.ndim == 1:
            X = X.reshape(1, -1)

        # 检查是否需要重新训练
        self.prediction_count += 1
        if (retrain_data is not None and
            self.prediction_count % self.retrain_frequency == 0):
            print(f"达到重训练频率 ({self.retrain_frequency})，开始重新训练...")
            X_retrain, y_retrain = retrain_data
            self.train_model(X_retrain, y_retrain)
            print("重新训练完成")

        # 预测
        pred_classes, pred_probs = self.predict(X)
        return pred_classes[0], pred_probs[0]

    def plot_results(self, results: Dict[str, Any], save_path: Optional[str] = None):
        """
        绘制滚动验证结果

        Args:
            results: rolling_train_validate的返回结果
            save_path: 保存路径，可选
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        _, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 1. 准确率随时间变化
        validation_results = results['validation_results']
        rounds = [r['round'] for r in validation_results]
        accuracies = [r['val_accuracy'] for r in validation_results]

        axes[0, 0].plot(rounds, accuracies, 'b-o', linewidth=2, markersize=4)
        axes[0, 0].set_title('验证准确率随轮次变化', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axhline(y=results['overall_accuracy'], color='r', linestyle='--',
                          label=f'总体准确率: {results["overall_accuracy"]:.3f}')
        axes[0, 0].legend()

        # 2. 混淆矩阵
        cm = confusion_matrix(results['true_labels'], results['predictions'])
        class_names = ['跌', '平', '涨']

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names, ax=axes[0, 1])
        axes[0, 1].set_title('混淆矩阵', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('预测类别')
        axes[0, 1].set_ylabel('真实类别')

        # 3. 预测分布 vs 真实分布
        pred_dist = np.bincount(results['predictions'], minlength=3)
        true_dist = np.bincount(results['true_labels'], minlength=3)

        x = np.arange(3)
        width = 0.35

        axes[1, 0].bar(x - width/2, true_dist, width, label='真实分布', alpha=0.8)
        axes[1, 0].bar(x + width/2, pred_dist, width, label='预测分布', alpha=0.8)
        axes[1, 0].set_title('预测分布 vs 真实分布', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('类别')
        axes[1, 0].set_ylabel('样本数量')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(class_names)
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 各类别准确率
        class_accuracies = []
        class_counts = []
        for i in range(3):
            mask = np.array(results['true_labels']) == i
            if np.sum(mask) > 0:
                class_acc = accuracy_score(np.array(results['true_labels'])[mask],
                                         np.array(results['predictions'])[mask])
                class_accuracies.append(class_acc)
                class_counts.append(np.sum(mask))
            else:
                class_accuracies.append(0)
                class_counts.append(0)

        bars = axes[1, 1].bar(class_names, class_accuracies, alpha=0.8, color=['red', 'gray', 'green'])
        axes[1, 1].set_title('各类别准确率', fontsize=14, fontweight='bold')
        axes[1, 1].set_ylabel('准确率')
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].grid(True, alpha=0.3)

        # 在柱状图上添加样本数量标签
        for i, (bar, count) in enumerate(zip(bars, class_counts)):
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{count}样本\n{height:.3f}',
                           ha='center', va='bottom', fontsize=10)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果图已保存到: {save_path}")

        plt.show()

    def get_feature_importance(self, top_n: int = 20) -> pd.DataFrame:
        """
        获取特征重要性

        Args:
            top_n: 返回前N个重要特征

        Returns:
            特征重要性DataFrame
        """
        if self.model is None:
            raise ValueError("模型尚未训练")

        importance = self.model.feature_importance(importance_type='gain')
        feature_imp = pd.DataFrame({
            'feature': self.feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)

        return feature_imp.head(top_n)


def simple_backtest(model_path: str, csv_file: str, batch_size: int = 500, validation_size: int = 100) -> Dict[str, Any]:
    """
    简单回测函数，类似main.py的批量预测模式

    Args:
        model_path: 模型保存路径
        csv_file: 数据文件路径
        batch_size: 训练批次大小
        validation_size: 验证批次大小

    Returns:
        回测结果
    """
    print("=== LightGBM简单回测开始 ===")

    # 初始化模型
    model = LightGBMStockModel(batch_size=batch_size, validation_size=validation_size)

    # 准备数据
    X, y, dates = model.prepare_data(csv_file)

    # 检查是否有已保存的模型
    if os.path.exists(model_path):
        print(f"加载已保存的模型: {model_path}")
        model.load_model(model_path)

        # 使用最后的数据进行预测
        test_start = len(X) - validation_size
        X_test = X[test_start:]
        y_test = y[test_start:]
        test_dates = dates[test_start:]

        print(f"使用模型预测最后 {len(X_test)} 个样本...")
        pred_classes, pred_probs = model.predict(X_test)
        accuracy = accuracy_score(y_test, pred_classes)

        print(f"预测准确率: {accuracy:.3f}")

        # 分类别准确率
        class_names = ['跌', '平', '涨']
        for i in range(3):
            mask = y_test == i
            if np.sum(mask) > 0:
                class_acc = accuracy_score(y_test[mask], pred_classes[mask])
                print(f"{class_names[i]}类准确率: {class_acc:.3f} ({np.sum(mask)}个样本)")

        return {
            'accuracy': accuracy,
            'predictions': pred_classes,
            'true_labels': y_test,
            'prediction_probs': pred_probs,
            'dates': test_dates
        }
    else:
        print("未找到已保存的模型，开始滚动训练验证...")
        # 进行滚动训练验证
        results = model.rolling_train_validate(X, y, dates)

        # 保存模型
        model.save_model(model_path)

        return results


def main():
    """主函数：演示LightGBM滚动训练验证"""
    print("=== LightGBM滚动训练验证演示 ===")

    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'  # 数据文件
    model_path = './model_lgbm.pkl'  # 模型保存路径
    batch_size = 500  # 每次训练样本数
    validation_size = 100  # 每次验证样本数

    # 自定义LightGBM参数
    custom_params = {
        'num_leaves': 31,
        'learning_rate': 0.05,
        'n_estimators': 100,
        'random_state': 42
    }

    try:
        # 初始化模型
        model = LightGBMStockModel(
            batch_size=batch_size,
            validation_size=validation_size,
            retrain_frequency=50,  # 每50次预测重新训练一次
            model_params=custom_params
        )

        # 准备数据
        print("准备数据...")
        X, y, dates = model.prepare_data(csv_file, window=200)

        # 滚动训练验证
        print("开始滚动训练验证...")
        results = model.rolling_train_validate(X, y, dates)

        # 保存模型
        print("保存模型...")
        model.save_model(model_path)

        # 绘制结果
        print("绘制结果...")
        model.plot_results(results, save_path='./lgbm_results.png')

        # 显示特征重要性
        print("\n=== 特征重要性 (Top 10) ===")
        feature_imp = model.get_feature_importance(top_n=10)
        print(feature_imp)

        # 保存详细结果
        results_df = pd.DataFrame(results['validation_results'])
        results_df.to_csv('./lgbm_validation_results.csv', index=False)
        print("详细结果已保存到: ./lgbm_validation_results.csv")

        print(f"\n=== 最终结果 ===")
        print(f"总体准确率: {results['overall_accuracy']:.3f}")
        print(f"总验证样本数: {len(results['predictions'])}")
        print(f"总训练轮次: {results['total_rounds']}")

        # 演示实盘预测
        print("\n=== 实盘预测演示 ===")
        # 使用最后一个样本进行预测
        last_sample = X[-1:]
        pred_class, pred_probs = model.predict_single(last_sample)
        class_names = ['跌', '平', '涨']
        print(f"预测结果: {class_names[pred_class]}")
        print(f"预测概率: 跌={pred_probs[0]:.3f}, 平={pred_probs[1]:.3f}, 涨={pred_probs[2]:.3f}")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()