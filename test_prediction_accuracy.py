#!/usr/bin/env python3
"""
测试LightGBM回归模型在最新数据上的预测准确率
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
# 解决中文乱码问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

sys.path.append('./models/lightgbm')
from lgbm_regression import LightGBMStockRegressionModel

def test_latest_predictions():
    """测试最新数据的预测准确率"""
    print("=== 测试最新数据预测准确率 ===")
    
    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'
    model_path = './model_lgbm_regression.pkl'
    
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"数据文件不存在: {csv_file}")
        return
    
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先运行训练脚本生成模型")
        return
    
    try:
        # 创建模型并加载
        model = LightGBMStockRegressionModel()
        model.load_model(model_path)
        
        # 准备数据
        print("准备数据...")
        X, y, dates = model.prepare_data(csv_file, window=200)
        
        # 使用最后1000个样本进行测试
        test_size = min(1000, len(X))
        X_test = X[-test_size:]
        y_test = y[-test_size:]
        dates_test = dates[-test_size:]
        
        print(f"使用最后 {test_size} 个样本进行测试")
        print(f"测试时间范围: {dates_test[0]} 到 {dates_test[-1]}")
        
        # 进行预测
        print("进行预测...")
        predictions = model.predict(X_test)
        
        # 计算准确率指标
        mse = mean_squared_error(y_test, predictions)
        mae = mean_absolute_error(y_test, predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, predictions)
        
        print(f"\n=== 预测准确率指标 ===")
        print(f"RMSE (均方根误差): {rmse:.6f}")
        print(f"MAE (平均绝对误差): {mae:.6f}")
        print(f"R² (决定系数): {r2:.4f}")
        
        # 计算方向准确率（预测涨跌方向是否正确）
        pred_direction = np.sign(predictions)
        actual_direction = np.sign(y_test)
        direction_accuracy = np.mean(pred_direction == actual_direction)
        
        print(f"方向预测准确率: {direction_accuracy:.4f} ({direction_accuracy*100:.2f}%)")
        
        # 分析预测误差分布
        errors = predictions - y_test
        print(f"\n=== 预测误差分析 ===")
        print(f"误差均值: {np.mean(errors):.6f}")
        print(f"误差标准差: {np.std(errors):.6f}")
        print(f"误差最大值: {np.max(errors):.6f}")
        print(f"误差最小值: {np.min(errors):.6f}")
        
        # 计算不同误差范围的样本比例
        error_ranges = [0.001, 0.002, 0.005, 0.01]
        print(f"\n=== 误差范围分析 ===")
        for threshold in error_ranges:
            within_range = np.mean(np.abs(errors) <= threshold)
            print(f"误差在 ±{threshold:.3f} 内的样本比例: {within_range:.4f} ({within_range*100:.2f}%)")
        
        # 分析不同收益率区间的预测表现
        print(f"\n=== 不同收益率区间预测表现 ===")
        
        # 定义收益率区间
        ranges = [
            (-np.inf, -0.005, "大跌 (<-0.5%)"),
            (-0.005, -0.002, "跌 (-0.5%~-0.2%)"),
            (-0.002, -0.001, "小跌 (-0.2%~-0.1%)"),
            (-0.001, 0.001, "震荡 (-0.1%~0.1%)"),
            (0.001, 0.002, "小涨 (0.1%~0.2%)"),
            (0.002, 0.005, "涨 (0.2%~0.5%)"),
            (0.005, np.inf, "大涨 (>0.5%)")
        ]
        
        for low, high, desc in ranges:
            mask = (y_test >= low) & (y_test < high)
            if np.sum(mask) > 0:
                range_mae = mean_absolute_error(y_test[mask], predictions[mask])
                range_r2 = r2_score(y_test[mask], predictions[mask]) if np.sum(mask) > 1 else 0
                print(f"{desc}: {np.sum(mask)} 样本, MAE={range_mae:.6f}, R²={range_r2:.4f}")
        
        # 显示一些具体的预测示例
        print(f"\n=== 预测示例 (最后10个样本) ===")
        print("日期\t\t\t实际收益率\t预测收益率\t误差")
        print("-" * 70)
        for i in range(-10, 0):
            date = dates_test[i]
            actual = y_test[i]
            pred = predictions[i]
            error = pred - actual
            print(f"{date}\t{actual:.6f}\t{pred:.6f}\t{error:.6f}")
        
        # 绘制预测结果图
        print(f"\n绘制预测结果图...")
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 预测值 vs 真实值散点图
        axes[0, 0].scatter(y_test, predictions, alpha=0.6, s=20)
        axes[0, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('实际收益率')
        axes[0, 0].set_ylabel('预测收益率')
        axes[0, 0].set_title(f'预测 vs 实际 (R² = {r2:.4f})')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 时间序列对比（最后200个样本）
        show_samples = min(200, len(y_test))
        x_range = range(len(y_test) - show_samples, len(y_test))
        axes[0, 1].plot(x_range, y_test[-show_samples:], label='实际值', alpha=0.7, linewidth=1)
        axes[0, 1].plot(x_range, predictions[-show_samples:], label='预测值', alpha=0.7, linewidth=1)
        axes[0, 1].set_xlabel('样本索引')
        axes[0, 1].set_ylabel('收益率')
        axes[0, 1].set_title(f'时间序列对比 (最后{show_samples}个样本)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 误差分布直方图
        axes[1, 0].hist(errors, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 0].axvline(0, color='red', linestyle='--', linewidth=2)
        axes[1, 0].set_xlabel('预测误差')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('预测误差分布')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 累积误差
        cumulative_errors = np.cumsum(np.abs(errors))
        axes[1, 1].plot(cumulative_errors)
        axes[1, 1].set_xlabel('样本索引')
        axes[1, 1].set_ylabel('累积绝对误差')
        axes[1, 1].set_title('累积绝对误差')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('./prediction_accuracy_analysis.png', dpi=300, bbox_inches='tight')
        print("预测分析图已保存到: ./prediction_accuracy_analysis.png")
        plt.show()
        
        # 保存详细结果
        results_df = pd.DataFrame({
            'date': dates_test,
            'actual_return': y_test,
            'predicted_return': predictions,
            'error': errors,
            'abs_error': np.abs(errors)
        })
        results_df.to_csv('./prediction_accuracy_results.csv', index=False)
        print("详细预测结果已保存到: ./prediction_accuracy_results.csv")
        
        print(f"\n=== 总结 ===")
        print(f"在最新的 {test_size} 个样本上:")
        print(f"• RMSE: {rmse:.6f} (越小越好)")
        print(f"• MAE: {mae:.6f} (越小越好)")
        print(f"• R²: {r2:.4f} (越接近1越好)")
        print(f"• 方向准确率: {direction_accuracy*100:.2f}% (随机猜测为50%)")
        
        # 评估模型表现
        if r2 > 0.1:
            performance = "较好"
        elif r2 > 0.05:
            performance = "一般"
        elif r2 > 0:
            performance = "较差"
        else:
            performance = "很差"
        
        print(f"• 模型表现评估: {performance}")
        
        if direction_accuracy > 0.55:
            direction_performance = "较好"
        elif direction_accuracy > 0.52:
            direction_performance = "一般"
        else:
            direction_performance = "较差"
        
        print(f"• 方向预测评估: {direction_performance}")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_latest_predictions()
