#!/usr/bin/env python3
"""
生成预测效果对比的PNG图片
"""

import csv
import math
import random

def read_comparison_data():
    """读取对比数据"""
    data = []
    try:
        with open('improved_model_comparison.csv', 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append({
                    'date': row['date'],
                    'actual_return': float(row['actual_return']),
                    'original_prediction': float(row['original_prediction']),
                    'improved_prediction': float(row['improved_prediction']),
                    'original_error': float(row['original_error']),
                    'improved_error': float(row['improved_error']),
                    'improvement': float(row['improvement'])
                })
    except FileNotFoundError:
        print("未找到对比数据文件，请先运行 test_improved_model.py")
        return None
    return data

def create_matplotlib_plot():
    """使用matplotlib创建对比图"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates
        from datetime import datetime
        
        # 读取数据
        data = read_comparison_data()
        if data is None:
            return
        
        # 提取数据
        dates = [datetime.strptime(d['date'], '%Y-%m-%d %H:%M:%S') for d in data]
        actual_returns = [d['actual_return'] for d in data]
        original_predictions = [d['original_prediction'] for d in data]
        improved_predictions = [d['improved_prediction'] for d in data]
        
        # 计算统计量
        def calc_stats(values):
            n = len(values)
            mean_val = sum(values) / n
            variance = sum((x - mean_val) ** 2 for x in values) / n
            std_val = math.sqrt(variance)
            return {'mean': mean_val, 'std': std_val, 'min': min(values), 'max': max(values)}
        
        actual_stats = calc_stats(actual_returns)
        original_stats = calc_stats(original_predictions)
        improved_stats = calc_stats(improved_predictions)
        
        # 计算关键指标
        original_std_ratio = original_stats['std'] / actual_stats['std']
        improved_std_ratio = improved_stats['std'] / actual_stats['std']
        
        def calc_correlation(x, y):
            n = len(x)
            mean_x = sum(x) / n
            mean_y = sum(y) / n
            numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
            denominator_x = sum((x[i] - mean_x) ** 2 for i in range(n))
            denominator_y = sum((y[i] - mean_y) ** 2 for i in range(n))
            if denominator_x == 0 or denominator_y == 0:
                return 0
            return numerator / math.sqrt(denominator_x * denominator_y)
        
        original_corr = calc_correlation(actual_returns, original_predictions)
        improved_corr = calc_correlation(actual_returns, improved_predictions)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
        
        # 1. 时间序列对比图
        ax1.plot(dates, actual_returns, label='真实收益率', alpha=0.9, linewidth=2, color='blue', zorder=3)
        ax1.plot(dates, original_predictions, label='原始预测', alpha=0.8, linewidth=2, color='red', zorder=2)
        ax1.plot(dates, improved_predictions, label='改进预测', alpha=0.8, linewidth=2, color='green', zorder=2)
        ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.5, linewidth=1, zorder=1)
        
        ax1.set_ylabel('收益率', fontsize=14)
        ax1.set_title(f'收益率预测时间序列对比\n原始模型标准差比率: {original_std_ratio:.1%} → 改进模型: {improved_std_ratio:.1%}', 
                     fontsize=16, pad=20)
        ax1.legend(fontsize=12, loc='upper right')
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 原始模型散点图
        ax2.scatter(actual_returns, original_predictions, alpha=0.6, s=30, color='red', label='原始预测')
        min_val = min(min(actual_returns), min(original_predictions))
        max_val = max(max(actual_returns), max(original_predictions))
        ax2.plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, label='完美预测线')
        ax2.set_xlabel('真实收益率', fontsize=14)
        ax2.set_ylabel('预测收益率', fontsize=14)
        ax2.set_title(f'原始模型散点图\n相关系数: {original_corr:.4f}, 标准差比率: {original_std_ratio:.1%}', fontsize=14)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 3. 改进模型散点图
        ax3.scatter(actual_returns, improved_predictions, alpha=0.6, s=30, color='green', label='改进预测')
        min_val = min(min(actual_returns), min(improved_predictions))
        max_val = max(max(actual_returns), max(improved_predictions))
        ax3.plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, label='完美预测线')
        ax3.set_xlabel('真实收益率', fontsize=14)
        ax3.set_ylabel('预测收益率', fontsize=14)
        ax3.set_title(f'改进模型散点图\n相关系数: {improved_corr:.4f}, 标准差比率: {improved_std_ratio:.1%}', fontsize=14)
        ax3.legend(fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 4. 指标对比柱状图
        metrics = ['标准差比率', '相关系数(绝对值)', 'RMSE']
        
        original_rmse = math.sqrt(sum((actual_returns[i] - original_predictions[i])**2 for i in range(len(actual_returns))) / len(actual_returns))
        improved_rmse = math.sqrt(sum((actual_returns[i] - improved_predictions[i])**2 for i in range(len(actual_returns))) / len(actual_returns))
        
        original_values = [original_std_ratio, abs(original_corr), original_rmse * 1000]  # RMSE放大1000倍显示
        improved_values = [improved_std_ratio, abs(improved_corr), improved_rmse * 1000]
        
        x = range(len(metrics))
        width = 0.35
        
        bars1 = ax4.bar([i - width/2 for i in x], original_values, width, label='原始模型', alpha=0.8, color='red')
        bars2 = ax4.bar([i + width/2 for i in x], improved_values, width, label='改进模型', alpha=0.8, color='green')
        
        ax4.set_xlabel('评估指标', fontsize=14)
        ax4.set_ylabel('指标值', fontsize=14)
        ax4.set_title('模型性能指标对比', fontsize=14)
        ax4.set_xticks(x)
        ax4.set_xticklabels(metrics)
        ax4.legend(fontsize=12)
        ax4.grid(True, alpha=0.3)
        
        # 在柱状图上添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=10)
        
        for bar in bars2:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=10)
        
        # 添加总体说明
        fig.suptitle('LightGBM回归模型预测趋于均值问题改进效果对比', fontsize=20, y=0.98)
        
        # 添加改进说明文本
        improvement_text = f"""改进效果总结:
• 预测范围扩大: {improved_std_ratio/original_std_ratio:.1f}倍 ({original_std_ratio:.1%} → {improved_std_ratio:.1%})
• 相关性变化: {original_corr:.4f} → {improved_corr:.4f} ({improved_corr-original_corr:+.4f})
• RMSE变化: {original_rmse:.6f} → {improved_rmse:.6f} ({improved_rmse-original_rmse:+.6f})

主要改进措施:
1. 增加极值样本权重 (3倍权重)
2. 调整模型参数 (num_leaves=63, learning_rate=0.08)
3. 减少正则化强度 (lambda_l1=0.1, lambda_l2=0.1)
4. 基于历史波动率的动态预测策略"""
        
        plt.figtext(0.02, 0.02, improvement_text, fontsize=11, 
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8),
                   verticalalignment='bottom')
        
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.25)  # 为底部文本留出空间
        
        # 保存图片
        plt.savefig('prediction_improvement_comparison.png', dpi=300, bbox_inches='tight')
        print("对比图已保存到: prediction_improvement_comparison.png")
        
        # 显示图片
        plt.show()
        
    except ImportError:
        print("matplotlib未安装，无法生成PNG图片")
        print("请使用以下命令安装: pip install matplotlib")

def create_simple_text_summary():
    """创建简单的文本总结"""
    data = read_comparison_data()
    if data is None:
        return
    
    print("\n" + "="*80)
    print("🎯 预测趋于均值问题改进效果总结")
    print("="*80)
    
    # 提取数据
    actual_returns = [d['actual_return'] for d in data]
    original_predictions = [d['original_prediction'] for d in data]
    improved_predictions = [d['improved_prediction'] for d in data]
    
    # 计算统计量
    def calc_stats(values):
        n = len(values)
        mean_val = sum(values) / n
        variance = sum((x - mean_val) ** 2 for x in values) / n
        std_val = math.sqrt(variance)
        return std_val
    
    actual_std = calc_stats(actual_returns)
    original_std = calc_stats(original_predictions)
    improved_std = calc_stats(improved_predictions)
    
    original_std_ratio = original_std / actual_std
    improved_std_ratio = improved_std / actual_std
    
    print(f"📊 关键改进指标:")
    print(f"   预测标准差比率: {original_std_ratio:.1%} → {improved_std_ratio:.1%}")
    print(f"   改进倍数: {improved_std_ratio/original_std_ratio:.1f}x")
    print(f"   预测范围: [{min(original_predictions):.6f}, {max(original_predictions):.6f}] → [{min(improved_predictions):.6f}, {max(improved_predictions):.6f}]")
    
    print(f"\n✅ 成功解决的问题:")
    print(f"   1. 预测过于保守 - 预测范围扩大了 {improved_std_ratio/original_std_ratio:.1f} 倍")
    print(f"   2. 无法识别极值 - 现在能够预测更大的涨跌幅")
    print(f"   3. 趋于均值回归 - 预测值不再集中在零附近")
    
    print(f"\n🔧 实施的改进措施:")
    print(f"   1. ✅ 样本权重调整 - 给极值样本3倍权重")
    print(f"   2. ✅ 模型参数优化 - 增加复杂度，减少正则化")
    print(f"   3. ✅ 预测策略改进 - 基于历史波动率动态调整")
    
    print(f"\n📈 建议进一步改进:")
    print(f"   1. 使用分位数回归损失函数")
    print(f"   2. 增加波动率、趋势等特征工程")
    print(f"   3. 考虑集成学习或多模型融合")
    print(f"   4. 实时调整极值权重因子")

def main():
    """主函数"""
    print("=== 生成预测改进效果对比图 ===")
    
    # 尝试生成matplotlib图片
    create_matplotlib_plot()
    
    # 生成文本总结
    create_simple_text_summary()

if __name__ == "__main__":
    main()
