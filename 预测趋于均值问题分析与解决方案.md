# 预测收益率趋于均值问题分析与解决方案

## 问题现象

从 `simple_prediction_results.csv` 的分析结果可以看出：

1. **预测值过于保守**：预测收益率大多集中在 -0.0003 到 +0.0003 之间
2. **真实值变化幅度更大**：真实收益率范围从 -0.009 到 +0.003
3. **模型趋于均值**：预测值几乎都聚集在接近0的位置，无法捕捉大涨大跌

## 根本原因分析

### 1. 损失函数问题
- **MSE损失的特性**：均方误差损失函数会惩罚大的预测误差，导致模型倾向于预测接近均值的安全值
- **极值样本稀少**：大涨大跌的样本相对较少，模型更容易学习到"保守预测"的策略

### 2. 模型参数问题
- **过度正则化**：L1/L2正则化过强，限制了模型的表达能力
- **叶子节点限制**：`num_leaves` 过小，模型复杂度不足以捕捉极值模式
- **学习率过低**：导致模型收敛到局部最优，无法充分学习极值特征

### 3. 数据不平衡问题
- **极值样本权重不足**：大涨大跌的样本在训练中权重与普通样本相同
- **特征工程不足**：缺乏专门识别极值情况的特征

## 解决方案

### 1. 样本权重调整 ✅
```python
def create_sample_weights(self, y: np.ndarray) -> np.ndarray:
    """给极值样本更高的权重"""
    q10 = np.percentile(y, 10)
    q90 = np.percentile(y, 90)
    
    weights = np.ones(len(y))
    extreme_mask = (y <= q10) | (y >= q90)
    weights[extreme_mask] *= self.extreme_weight_factor  # 3倍权重
    
    return weights
```

### 2. 模型参数优化 ✅
```python
improved_params = {
    'num_leaves': 63,           # 增加叶子数，提高复杂度
    'learning_rate': 0.08,      # 适当提高学习率
    'feature_fraction': 0.85,   # 保留更多特征信息
    'bagging_fraction': 0.75,   # 减少过度采样
    'min_data_in_leaf': 10,     # 降低叶子节点最小样本数
    'min_gain_to_split': 0.01,  # 降低分裂阈值
    'lambda_l1': 0.1,           # 减少L1正则化
    'lambda_l2': 0.1,           # 减少L2正则化
    'n_estimators': 150,        # 增加树的数量
}
```

### 3. 损失函数改进 (待实现)
- **分位数回归**：使用分位数损失，专门预测极值
- **Huber损失**：对异常值更鲁棒的损失函数
- **自定义损失**：针对极值预测设计的损失函数

### 4. 特征工程增强 (待实现)
- **波动率特征**：滚动标准差、ATR等
- **极值指标**：RSI、布林带位置等
- **市场情绪特征**：成交量异常、价格跳跃等

## 评估指标

### 传统指标
- **RMSE/MAE**：整体预测误差
- **R²**：拟合优度
- **相关系数**：线性相关性

### 极值预测专用指标
- **标准差比率**：`pred_std / true_std`，理想值接近1
- **极值相关性**：大涨大跌样本的预测相关性
- **极值捕获率**：正确预测极值方向的比例

## 实施步骤

### 第一阶段：基础改进 ✅
1. 实现样本权重调整
2. 优化模型参数
3. 增加极值预测能力分析

### 第二阶段：高级改进 (计划中)
1. 实现分位数回归
2. 增强特征工程
3. 自定义损失函数

### 第三阶段：集成方法 (计划中)
1. 多模型集成
2. 分层预测策略
3. 动态权重调整

## 预期效果

### 改进前
- 预测标准差 / 真实标准差 ≈ 0.1-0.3
- 极值预测相关性 ≈ 0.0-0.2
- 大涨大跌基本无法预测

### 改进后目标
- 预测标准差 / 真实标准差 ≈ 0.6-0.8
- 极值预测相关性 ≈ 0.3-0.5
- 能够识别部分大涨大跌趋势

## 使用方法

```python
# 创建改进的模型
model = ImprovedLightGBMRegression(
    batch_size=300,
    validation_size=100,
    extreme_weight_factor=3.0  # 极值权重因子
)

# 训练和测试
results = test_improved_model()
```

## 注意事项

1. **过拟合风险**：增加模型复杂度可能导致过拟合，需要仔细调参
2. **计算成本**：更复杂的模型训练时间更长
3. **实盘验证**：改进效果需要在实盘数据上验证
4. **风险控制**：极值预测的准确性仍然有限，不应过度依赖

## 下一步计划

1. 运行改进模型，对比效果
2. 分析结果，进一步优化参数
3. 实现分位数回归版本
4. 增加更多极值识别特征
5. 考虑集成学习方法
