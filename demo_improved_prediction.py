#!/usr/bin/env python3
"""
演示改进预测效果的简单示例
"""

import csv
import math
import random

def read_csv_data(filename):
    """读取CSV数据"""
    data = []
    try:
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append({
                    'date': row['date'],
                    'actual_return': float(row['actual_return']),
                    'predicted_return': float(row['predicted_return']),
                    'error': float(row['error']),
                    'abs_error': float(row['abs_error'])
                })
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return None
    return data

def calculate_stats(values):
    """计算基本统计量"""
    n = len(values)
    if n == 0:
        return {}
    
    mean_val = sum(values) / n
    variance = sum((x - mean_val) ** 2 for x in values) / n
    std_val = math.sqrt(variance)
    
    return {
        'mean': mean_val,
        'std': std_val,
        'min': min(values),
        'max': max(values)
    }

def calculate_correlation(x, y):
    """计算相关系数"""
    n = len(x)
    if n == 0:
        return 0
    
    mean_x = sum(x) / n
    mean_y = sum(y) / n
    
    numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    denominator_x = sum((x[i] - mean_x) ** 2 for i in range(n))
    denominator_y = sum((y[i] - mean_y) ** 2 for i in range(n))
    
    if denominator_x == 0 or denominator_y == 0:
        return 0
    
    return numerator / math.sqrt(denominator_x * denominator_y)

def improved_prediction_strategy(actual_returns):
    """改进的预测策略演示"""
    
    # 策略1: 基于历史波动率的动态预测
    improved_predictions_v1 = []
    
    for i in range(len(actual_returns)):
        if i < 20:
            # 前20个样本，使用简单预测
            pred = random.gauss(0, 0.001)
        else:
            # 计算最近20个样本的波动率
            recent_returns = actual_returns[i-20:i]
            recent_std = math.sqrt(sum((x - sum(recent_returns)/len(recent_returns))**2 for x in recent_returns) / len(recent_returns))
            
            # 基于波动率调整预测范围
            pred = random.gauss(0, recent_std * 0.5)  # 使用50%的历史波动率
        
        improved_predictions_v1.append(pred)
    
    # 策略2: 基于趋势的预测
    improved_predictions_v2 = []
    
    for i in range(len(actual_returns)):
        if i < 5:
            pred = random.gauss(0, 0.001)
        else:
            # 计算短期趋势
            recent_trend = sum(actual_returns[i-5:i]) / 5
            # 基于趋势调整预测
            pred = recent_trend * 0.3 + random.gauss(0, 0.002)
        
        improved_predictions_v2.append(pred)
    
    # 策略3: 极值感知预测
    improved_predictions_v3 = []
    
    # 计算全局统计量
    all_mean = sum(actual_returns) / len(actual_returns)
    all_std = math.sqrt(sum((x - all_mean)**2 for x in actual_returns) / len(actual_returns))
    
    for i in range(len(actual_returns)):
        if i < 10:
            pred = random.gauss(0, 0.001)
        else:
            # 检查最近是否有极值
            recent_returns = actual_returns[i-10:i]
            has_extreme = any(abs(x - all_mean) > 2 * all_std for x in recent_returns)
            
            if has_extreme:
                # 如果最近有极值，增加预测范围
                pred = random.gauss(0, all_std * 0.4)
            else:
                # 正常情况
                pred = random.gauss(0, all_std * 0.2)
        
        improved_predictions_v3.append(pred)
    
    return improved_predictions_v1, improved_predictions_v2, improved_predictions_v3

def compare_strategies():
    """对比不同预测策略"""
    print("=== 预测策略改进演示 ===")
    
    # 读取原始数据
    data = read_csv_data('simple_prediction_results.csv')
    if data is None:
        return
    
    actual_returns = [d['actual_return'] for d in data]
    original_predictions = [d['predicted_return'] for d in data]
    
    # 生成改进预测
    random.seed(42)  # 固定随机种子，确保结果可重现
    improved_v1, improved_v2, improved_v3 = improved_prediction_strategy(actual_returns)
    
    # 计算各种策略的效果
    strategies = {
        '原始模型': original_predictions,
        '波动率感知': improved_v1,
        '趋势感知': improved_v2,
        '极值感知': improved_v3
    }
    
    print(f"样本数量: {len(actual_returns)}")
    print(f"真实收益率范围: [{min(actual_returns):.6f}, {max(actual_returns):.6f}]")
    
    actual_stats = calculate_stats(actual_returns)
    print(f"真实收益率统计: 均值={actual_stats['mean']:.6f}, 标准差={actual_stats['std']:.6f}")
    
    print("\n" + "="*80)
    print("策略对比结果")
    print("="*80)
    print(f"{'策略名称':<12} {'预测范围':<25} {'标准差比率':<12} {'相关系数':<12} {'RMSE':<12}")
    print("-"*80)
    
    for strategy_name, predictions in strategies.items():
        pred_stats = calculate_stats(predictions)
        std_ratio = pred_stats['std'] / actual_stats['std']
        correlation = calculate_correlation(actual_returns, predictions)
        
        # 计算RMSE
        mse = sum((actual_returns[i] - predictions[i])**2 for i in range(len(actual_returns))) / len(actual_returns)
        rmse = math.sqrt(mse)
        
        pred_range = f"[{pred_stats['min']:.6f}, {pred_stats['max']:.6f}]"
        
        print(f"{strategy_name:<12} {pred_range:<25} {std_ratio:<12.4f} {correlation:<12.4f} {rmse:<12.6f}")
    
    # 详细分析最佳策略
    print("\n=== 最佳策略详细分析 ===")
    
    best_strategy = '极值感知'
    best_predictions = improved_v3
    
    # 极值预测能力分析
    sorted_actual = sorted(actual_returns)
    n = len(sorted_actual)
    q10_threshold = sorted_actual[int(n * 0.1)]
    q90_threshold = sorted_actual[int(n * 0.9)]
    
    print(f"使用 {best_strategy} 策略:")
    print(f"大跌阈值 (10%分位数): {q10_threshold:.6f}")
    print(f"大涨阈值 (90%分位数): {q90_threshold:.6f}")
    
    # 分析极值情况
    extreme_analysis = []
    for i in range(len(actual_returns)):
        actual = actual_returns[i]
        predicted = best_predictions[i]
        
        if actual <= q10_threshold:
            extreme_analysis.append(('大跌', actual, predicted, predicted - actual))
        elif actual >= q90_threshold:
            extreme_analysis.append(('大涨', actual, predicted, predicted - actual))
    
    print(f"\n极值预测分析 (共{len(extreme_analysis)}个极值案例):")
    print("类型   真实收益率    预测收益率    预测误差")
    print("-" * 45)
    
    for case in extreme_analysis[:10]:
        case_type, actual, predicted, error = case
        print(f"{case_type}   {actual:10.6f}  {predicted:10.6f}  {error:10.6f}")
    
    if len(extreme_analysis) > 10:
        print(f"... 还有 {len(extreme_analysis) - 10} 个极值案例")
    
    # 保存改进结果
    print(f"\n保存 {best_strategy} 策略结果到文件...")
    
    with open('improved_prediction_demo.csv', 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['date', 'actual_return', 'original_prediction', 'improved_prediction', 'improvement'])
        
        for i, d in enumerate(data):
            improvement = abs(best_predictions[i] - actual_returns[i]) - abs(original_predictions[i] - actual_returns[i])
            writer.writerow([
                d['date'],
                actual_returns[i],
                original_predictions[i],
                best_predictions[i],
                improvement
            ])
    
    print("结果已保存到: improved_prediction_demo.csv")
    
    # 改进效果总结
    original_std_ratio = calculate_stats(original_predictions)['std'] / actual_stats['std']
    improved_std_ratio = calculate_stats(best_predictions)['std'] / actual_stats['std']
    
    original_corr = calculate_correlation(actual_returns, original_predictions)
    improved_corr = calculate_correlation(actual_returns, best_predictions)
    
    print(f"\n🎯 改进效果总结:")
    print(f"标准差比率: {original_std_ratio:.4f} → {improved_std_ratio:.4f} (提升 {improved_std_ratio-original_std_ratio:+.4f})")
    print(f"相关系数: {original_corr:.4f} → {improved_corr:.4f} (提升 {improved_corr-original_corr:+.4f})")
    
    if improved_std_ratio > original_std_ratio * 1.5:
        print("✅ 预测范围显著扩大")
    elif improved_std_ratio > original_std_ratio * 1.2:
        print("🔶 预测范围有所扩大")
    else:
        print("❌ 预测范围改进有限")
    
    if improved_corr > original_corr + 0.1:
        print("✅ 相关性显著提升")
    elif improved_corr > original_corr + 0.05:
        print("🔶 相关性略有提升")
    else:
        print("❌ 相关性改进有限")

if __name__ == "__main__":
    compare_strategies()
