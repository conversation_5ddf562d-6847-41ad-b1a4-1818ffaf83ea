#!/usr/bin/env python3
"""
可视化改进效果对比
"""

import csv
import math

def read_csv_data(filename):
    """读取CSV数据"""
    data = []
    try:
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append({
                    'date': row['date'],
                    'actual_return': float(row['actual_return']),
                    'original_prediction': float(row['original_prediction']),
                    'improved_prediction': float(row['improved_prediction']),
                    'improvement': float(row['improvement'])
                })
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return None
    return data

def create_comparison_scatter_plot(actual, original_pred, improved_pred, title, width=80, height=25):
    """创建对比散点图"""
    print(f"\n=== {title} ===")
    
    if not actual or not original_pred or not improved_pred:
        print("无数据")
        return
    
    # 计算数据范围
    all_values = actual + original_pred + improved_pred
    x_min, x_max = min(actual), max(actual)
    y_min, y_max = min(all_values), max(all_values)
    
    # 添加边距
    x_range = x_max - x_min
    y_range = y_max - y_min
    
    if x_range == 0:
        x_range = 1
    if y_range == 0:
        y_range = 1
    
    x_min -= x_range * 0.1
    x_max += x_range * 0.1
    y_min -= y_range * 0.1
    y_max += y_range * 0.1
    
    # 创建两个画布
    canvas_original = [[' ' for _ in range(width)] for _ in range(height)]
    canvas_improved = [[' ' for _ in range(width)] for _ in range(height)]
    
    # 绘制原始预测
    for i in range(len(actual)):
        x_pos = int((actual[i] - x_min) / (x_max - x_min) * (width - 1))
        y_pos = int((original_pred[i] - y_min) / (y_max - y_min) * (height - 1))
        y_pos = height - 1 - y_pos
        
        if 0 <= x_pos < width and 0 <= y_pos < height:
            canvas_original[y_pos][x_pos] = '●'
    
    # 绘制改进预测
    for i in range(len(actual)):
        x_pos = int((actual[i] - x_min) / (x_max - x_min) * (width - 1))
        y_pos = int((improved_pred[i] - y_min) / (y_max - y_min) * (height - 1))
        y_pos = height - 1 - y_pos
        
        if 0 <= x_pos < width and 0 <= y_pos < height:
            canvas_improved[y_pos][x_pos] = '◆'
    
    # 绘制完美预测线
    for canvas in [canvas_original, canvas_improved]:
        for x_pos in range(width):
            x_val = x_min + (x_pos / (width - 1)) * (x_max - x_min)
            y_val = x_val  # y = x
            
            if y_min <= y_val <= y_max:
                y_pos = int((y_val - y_min) / (y_max - y_min) * (height - 1))
                y_pos = height - 1 - y_pos
                
                if 0 <= y_pos < height and canvas[y_pos][x_pos] == ' ':
                    canvas[y_pos][x_pos] = '─'
    
    # 打印原始模型图表
    print("原始模型预测:")
    print("预测收益率")
    print("│")
    
    for row in canvas_original:
        print("│" + "".join(row))
    
    print("└" + "─" * width)
    print(" " * (width//2 - 6) + "真实收益率")
    
    # 打印改进模型图表
    print("\n改进模型预测:")
    print("预测收益率")
    print("│")
    
    for row in canvas_improved:
        print("│" + "".join(row))
    
    print("└" + "─" * width)
    print(" " * (width//2 - 6) + "真实收益率")
    
    print(f"\n图例: ● 原始预测  ◆ 改进预测  ─ 完美预测线 (y=x)")

def create_side_by_side_comparison(actual, original_pred, improved_pred, width=40, height=20):
    """创建并排对比图"""
    print(f"\n=== 并排对比图 ===")
    
    # 计算数据范围
    all_values = actual + original_pred + improved_pred
    x_min, x_max = min(actual), max(actual)
    y_min, y_max = min(all_values), max(all_values)
    
    # 添加边距
    x_range = x_max - x_min
    y_range = y_max - y_min
    
    if x_range == 0:
        x_range = 1
    if y_range == 0:
        y_range = 1
    
    x_min -= x_range * 0.1
    x_max += x_range * 0.1
    y_min -= y_range * 0.1
    y_max += y_range * 0.1
    
    # 创建两个画布
    canvas_original = [[' ' for _ in range(width)] for _ in range(height)]
    canvas_improved = [[' ' for _ in range(width)] for _ in range(height)]
    
    # 绘制数据点和完美预测线
    for canvas, predictions in [(canvas_original, original_pred), (canvas_improved, improved_pred)]:
        # 绘制数据点
        for i in range(len(actual)):
            x_pos = int((actual[i] - x_min) / (x_max - x_min) * (width - 1))
            y_pos = int((predictions[i] - y_min) / (y_max - y_min) * (height - 1))
            y_pos = height - 1 - y_pos
            
            if 0 <= x_pos < width and 0 <= y_pos < height:
                canvas[y_pos][x_pos] = '●'
        
        # 绘制完美预测线
        for x_pos in range(width):
            x_val = x_min + (x_pos / (width - 1)) * (x_max - x_min)
            y_val = x_val
            
            if y_min <= y_val <= y_max:
                y_pos = int((y_val - y_min) / (y_max - y_min) * (height - 1))
                y_pos = height - 1 - y_pos
                
                if 0 <= y_pos < height and canvas[y_pos][x_pos] == ' ':
                    canvas[y_pos][x_pos] = '─'
    
    # 并排打印
    print("原始模型" + " " * (width - 8) + "│" + " " * 8 + "改进模型")
    print("─" * width + "┼" + "─" * width)
    
    for i in range(height):
        left_row = "".join(canvas_original[i])
        right_row = "".join(canvas_improved[i])
        print(left_row + "│" + right_row)
    
    print("─" * width + "┼" + "─" * width)
    print(" " * (width//2 - 6) + "真实收益率" + " " * (width//2 - 6) + "│" + " " * (width//2 - 6) + "真实收益率")

def main():
    """主函数"""
    print("=== 预测改进效果可视化对比 ===")
    
    # 读取改进结果数据
    data = read_csv_data('improved_prediction_demo.csv')
    if data is None:
        return
    
    # 提取数据
    actual_returns = [d['actual_return'] for d in data]
    original_predictions = [d['original_prediction'] for d in data]
    improved_predictions = [d['improved_prediction'] for d in data]
    improvements = [d['improvement'] for d in data]
    
    print(f"样本数量: {len(data)}")
    
    # 计算统计量
    def calc_stats(values):
        n = len(values)
        mean_val = sum(values) / n
        variance = sum((x - mean_val) ** 2 for x in values) / n
        std_val = math.sqrt(variance)
        return {'mean': mean_val, 'std': std_val, 'min': min(values), 'max': max(values)}
    
    actual_stats = calc_stats(actual_returns)
    original_stats = calc_stats(original_predictions)
    improved_stats = calc_stats(improved_predictions)
    
    print(f"\n=== 统计对比 ===")
    print(f"真实收益率: 均值={actual_stats['mean']:.6f}, 标准差={actual_stats['std']:.6f}")
    print(f"原始预测:   均值={original_stats['mean']:.6f}, 标准差={original_stats['std']:.6f}")
    print(f"改进预测:   均值={improved_stats['mean']:.6f}, 标准差={improved_stats['std']:.6f}")
    
    print(f"\n=== 关键指标对比 ===")
    original_std_ratio = original_stats['std'] / actual_stats['std']
    improved_std_ratio = improved_stats['std'] / actual_stats['std']
    
    print(f"标准差比率:")
    print(f"  原始模型: {original_std_ratio:.4f} ({original_std_ratio*100:.1f}%)")
    print(f"  改进模型: {improved_std_ratio:.4f} ({improved_std_ratio*100:.1f}%)")
    print(f"  改进幅度: {improved_std_ratio - original_std_ratio:+.4f}")
    
    print(f"\n预测范围:")
    print(f"  真实值:   [{actual_stats['min']:.6f}, {actual_stats['max']:.6f}]")
    print(f"  原始预测: [{original_stats['min']:.6f}, {original_stats['max']:.6f}]")
    print(f"  改进预测: [{improved_stats['min']:.6f}, {improved_stats['max']:.6f}]")
    
    # 创建可视化对比
    create_side_by_side_comparison(actual_returns, original_predictions, improved_predictions)
    
    # 分析改进效果
    print(f"\n=== 改进效果分析 ===")
    
    positive_improvements = sum(1 for imp in improvements if imp < 0)  # 负值表示误差减小
    total_samples = len(improvements)
    
    print(f"预测改进的样本数: {positive_improvements}/{total_samples} ({positive_improvements/total_samples*100:.1f}%)")
    
    # 极值情况分析
    sorted_actual = sorted(actual_returns)
    n = len(sorted_actual)
    q10_threshold = sorted_actual[int(n * 0.1)]
    q90_threshold = sorted_actual[int(n * 0.9)]
    
    extreme_improvements = []
    for i, d in enumerate(data):
        actual = d['actual_return']
        if actual <= q10_threshold or actual >= q90_threshold:
            original_error = abs(d['original_prediction'] - actual)
            improved_error = abs(d['improved_prediction'] - actual)
            extreme_improvements.append(improved_error - original_error)
    
    if extreme_improvements:
        avg_extreme_improvement = sum(extreme_improvements) / len(extreme_improvements)
        extreme_better_count = sum(1 for imp in extreme_improvements if imp < 0)
        
        print(f"\n极值情况改进:")
        print(f"  极值样本数: {len(extreme_improvements)}")
        print(f"  改进样本数: {extreme_better_count}/{len(extreme_improvements)} ({extreme_better_count/len(extreme_improvements)*100:.1f}%)")
        print(f"  平均误差变化: {avg_extreme_improvement:+.6f}")
    
    # 总结
    print(f"\n🎯 改进总结:")
    
    if improved_std_ratio > original_std_ratio * 2:
        print("✅ 预测范围显著扩大，模型不再过度保守")
    elif improved_std_ratio > original_std_ratio * 1.5:
        print("🔶 预测范围有所扩大，但仍有改进空间")
    else:
        print("❌ 预测范围改进有限")
    
    if improved_std_ratio > 0.5:
        print("✅ 预测标准差达到合理水平")
    elif improved_std_ratio > 0.3:
        print("🔶 预测标准差有所改善")
    else:
        print("❌ 预测标准差仍然偏小")
    
    print(f"\n💡 进一步改进建议:")
    print("1. 使用真实的机器学习模型而非随机模拟")
    print("2. 增加样本权重，让模型更关注极值")
    print("3. 调整损失函数，减少对大误差的过度惩罚")
    print("4. 增加波动率、趋势等特征工程")
    print("5. 考虑分位数回归或集成学习方法")

if __name__ == "__main__":
    main()
